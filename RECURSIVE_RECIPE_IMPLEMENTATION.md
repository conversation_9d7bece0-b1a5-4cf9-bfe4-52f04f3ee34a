# Recursive Recipe Processing Implementation

## Overview
This implementation adds support for **nested/recursive recipe processing** to the IFC POS system. Now when a recipe contains another recipe as an ingredient, the system will automatically expand and process all sub-recipe ingredients recursively.

## Problem Solved
**Before**: If product 12801 (recipe) was added as an ingredient to product 128010 (another recipe), the system would stop and not process 12801's ingredients.

**After**: The system now recursively processes nested recipes, expanding all ingredients at all levels while maintaining proper quantity calculations and preventing infinite loops.

## Changes Made

### 1. Modified `GetProductionRecipyData()` Function
- **File**: `IFC_Main_Frm.vb` (lines 2104-2113)
- **Change**: Replaced the simple recipe processing loop with a call to the new recursive function
- **Added**: Circular reference detection using a `List(Of Integer)` to track processed products

### 2. Added `ProcessRecipeRecursively()` Function
- **File**: `IFC_Main_Frm.vb` (lines 2140-2190)
- **Purpose**: Recursively processes recipe ingredients, detecting and expanding nested recipes
- **Features**:
  - Circular reference prevention
  - Quantity multiplier calculation for nested levels
  - Automatic detection of recipe ingredients (IsRecipe=True AND IsProduction=True)
  - Proper cleanup of tracking lists

### 3. Added `AddIngredientToRecipeGrid()` Function
- **File**: `IFC_Main_Frm.vb` (lines 2199-2258)
- **Purpose**: Adds regular (non-recipe) ingredients to the recipe grid
- **Features**:
  - Applies quantity multipliers for nested recipe calculations
  - Maintains all existing functionality (stock checking, patch handling, etc.)
  - Links ingredients to the root product for proper cost calculation

## How It Works

### Recursive Processing Flow:
1. **Start**: Process main recipe (e.g., product 128010)
2. **Check Each Ingredient**: For each ingredient in the recipe:
   - Query the ingredient's product data
   - Check if it has `IsRecipe=True` AND `IsProduction=True`
3. **If Ingredient is a Recipe**:
   - Calculate quantity multiplier (parent quantity × ingredient quantity)
   - Recursively call `ProcessRecipeRecursively()` for the sub-recipe
   - Process all of the sub-recipe's ingredients
4. **If Ingredient is Regular**:
   - Add directly to the recipe grid with proper quantity calculations
5. **Circular Reference Protection**: Track processed products to prevent infinite loops

### Example Scenario:
```
Recipe 128010 (Main Product)
├── Ingredient A (regular) - Quantity: 2
├── Recipe 12801 (sub-recipe) - Quantity: 1
│   ├── Ingredient B (regular) - Quantity: 3
│   ├── Ingredient C (regular) - Quantity: 1.5
│   └── Recipe 12802 (sub-sub-recipe) - Quantity: 0.5
│       ├── Ingredient D (regular) - Quantity: 2
│       └── Ingredient E (regular) - Quantity: 4
└── Ingredient F (regular) - Quantity: 1
```

**Result**: All ingredients (A, B, C, D, E, F) will be added to the recipe grid with properly calculated quantities based on their nesting level.

## Testing Instructions

### Test Case 1: Basic Nested Recipe
1. Create a recipe product (e.g., 12801) with `IsRecipe=True` and `IsProduction=True`
2. Add some regular ingredients to this recipe
3. Create another recipe product (e.g., 128010) with `IsRecipe=True` and `IsProduction=True`
4. Add product 12801 as an ingredient to product 128010
5. Process product 128010 in the POS system
6. **Expected Result**: Both 12801's ingredients and 128010's other ingredients should appear in the recipe grid

### Test Case 2: Multiple Nesting Levels
1. Create Recipe A with regular ingredients
2. Create Recipe B that includes Recipe A as an ingredient
3. Create Recipe C that includes Recipe B as an ingredient
4. Process Recipe C
5. **Expected Result**: All ingredients from all levels should be expanded and displayed

### Test Case 3: Circular Reference Protection
1. Create Recipe A that includes Recipe B as an ingredient
2. Create Recipe B that includes Recipe A as an ingredient
3. Process either recipe
4. **Expected Result**: System should not hang or crash, should process available ingredients without infinite loop

## Benefits

1. **Complete Ingredient Visibility**: All ingredients at all nesting levels are now visible
2. **Accurate Quantity Calculations**: Quantities are properly multiplied through nesting levels
3. **Proper Cost Calculations**: Costs are calculated based on actual ingredient requirements
4. **Inventory Management**: Stock deductions work correctly for all actual ingredients
5. **Circular Reference Safety**: System is protected against infinite loops
6. **Backward Compatibility**: Existing single-level recipes continue to work unchanged

## Technical Notes

- The implementation maintains all existing functionality (patch handling, expiry dates, stock checking)
- Quantity multipliers ensure accurate calculations through multiple nesting levels
- The circular reference detection prevents infinite loops in case of recipe cycles
- All existing error handling and validation remains intact
- Performance impact is minimal for simple recipes, scales appropriately for complex nested recipes
