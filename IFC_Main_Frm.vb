﻿Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports System.IO
Imports System.Threading
Imports C1.Win.C1FlexGrid

Public Class IFC_Main_Frm
    Dim Conn As New Conn_Cls
    Public LoadSetting As Boolean
    Dim IFC_Cls As New IFC_Transaction
    Public IsProduction As Boolean
    Dim ClsStock As New OnHand_Cls
    Dim ClsSetting As New Setting_Cls
    Dim IsManuel As Boolean = True
    Dim ClsValid As New CLS_Validation
    Public Enum TablesCreated
        POSSetting
        Sales_POS
        CostCenterLinkPOS
        StockOnHandTbl_POS
        'Beesho = 0
        'Okays = 0
    End Enum

    Sub New()

        ' This call is required by the designer.
        InitializeComponent()
        Try

            IsManuel = Conn.Ismanuel
            Lbltitle.Text = "InterFace Supply Chain Management System With " & Conn.POS
            'Conn.IsUp = True
            '' Add any initialization after the InitializeComponent() call.
            'Conn.ConnPOS()
            ''   Setting_Frm.ShowDialog()
            'Conn.ConnSCM()
            'Conn.IsUp = False
            'If IsManuel = False Then
            ChecktablesCreated()
            Dim X As Integer = TablesHAsData(TablesCreated.POSSetting.ToString())


            '   If True Then
            Dim cmd As New SqlCommand
            Dim Sql As String = " IF not EXISTS(SELECT 1 FROM sys.columns "
            Sql = Sql & "       WHERE Name = N'ChDID'"
            Sql = Sql & "      AND Object_ID = Object_ID(N'dbo.Sales_POS'))"
            Sql = Sql & "   BEGIN"
            Sql = Sql & "   ALTER TABLE Sales_POS"
            Sql = Sql & "   ADD  [ChDID] [int]"
            Sql = Sql & "   CONSTRAINT [DF_Sales_POS_ChDID]  DEFAULT ((0)) "
            Sql = Sql & "   END "
            Conn.OpenConnection()
            cmd.CommandText = Sql '"select * from Inventory_Period where Branch_No=1 and Y_Period=2017   order by Period_No"
            cmd.Connection = Conn.SqlConn
            cmd.ExecuteNonQuery()
            Conn.CloseConnection()


            'End If
            If LoadSetting Or X = 0 Then
                Dim f As New Setting_Frm(True)
                f.ShowDialog()
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub

    Public Sub ChecktablesCreated()
        LoadSetting = False
        Dim ViewOrTable As Boolean
        Dim Createdd As Boolean = False
        For Each Itables As TablesCreated In [Enum].GetValues(GetType(TablesCreated))
            '  If Itables.ToString() = "GP_File_dts" Then ViewOrTable = True
            ViewOrTable = CInt(Itables)

            Createdd = ExistOrNot(Itables.ToString(), ViewOrTable)

            If Createdd = False Then
                CreateTableSQL(Itables)
                LoadSetting = True
            End If
            ViewOrTable = False
        Next


    End Sub

    Public Function ExistOrNot(TableName As String, vieww As Boolean) As Boolean
        Dim Ad As SqlDataAdapter
        Dim Dt As New DataTable
        Dim ViewTables As String
        Dim Ext As Boolean = False
        Dt.Clear()


        'If vieww = False Then
        '    ViewTables = "views"
        'Else
        '    ViewTables = "tables"
        'End If

        If TableName = "Sales_POS" Or TableName = "StockOnHandTbl_POS" Or TableName = "POSSetting" Or TableName = "CostCenterLinkPOS" Then
            ViewTables = "tables"
        Else
            ViewTables = "views"
        End If
        Ad = New SqlDataAdapter("SELECT * FROM sys." & ViewTables & "  WHERE name ='" & TableName & "'", Conn.SqlConn)
        Ad.Fill(Dt)

        If Dt.Rows.Count > 0 Then
            Ext = True
        End If
        Return Ext
    End Function

    Sub CreateTableSQL(TTables As TablesCreated)
        Dim Sql As String = String.Empty
        Dim DoneTable As Boolean = False


        If TablesCreated.POSSetting = TTables Then
            Sql = "CREATE TABLE [POSSetting] ([Ser] [int] NULL, " ' [Company_IdSCM] [int] NULL, "
            'Sql = Sql & " [Company_NameSCM] [nvarchar](150) NULL,"
            'Sql = Sql & " [Branch_IdSCM] [int] NULL,"
            'Sql = Sql & " [Branch_NameSCM] [nvarchar](150) NULL,"
            'Sql = Sql & " [Store_IdSCM] [int] NULL,"
            'Sql = Sql & " [Store_NameSCM] [nvarchar](150) NULL,"
            Sql = Sql & " [Company_IdPOS] [int] NULL,"
            Sql = Sql & " [Company_NamePOS] [nvarchar](150) NULL,"
            Sql = Sql & " [Brand_IdPOS] [int] NULL,"
            Sql = Sql & " [Brand_NamePOS] [nvarchar](150) NULL,"
            Sql = Sql & " [CostCenter_IdPOS] [int] NULL,"
            Sql = Sql & " [CostCenter_NamePOS] [nvarchar](150) NULL,	[IsDelete] [bit] NULL) ON [PRIMARY] "
            Sql = Sql & "  ALTER TABLE [POSSetting] ADD  CONSTRAINT [DF_POSSetting_IsDelete]  DEFAULT ((0)) FOR [IsDelete]     "
            DoneTable = True
        End If

        If TablesCreated.StockOnHandTbl_POS = TTables Then

            Sql = "CREATE TABLE [dbo].[StockOnHandTbl_POS](
	                [Ser] [int] IDENTITY(1,1) NOT NULL,
	                [Product_Id] [int] NULL,
	                [Product_Name] [nvarchar](150) NULL,
	                [Product_Code] [nvarchar](50) NULL,
	                [CostCenter_Id] [int] NULL,
	                [Quntity] [decimal](24, 4) NULL
                    ) ON [PRIMARY]"

            DoneTable = True
        End If
        If TablesCreated.Sales_POS = TTables Then

            Sql = "CREATE TABLE  [Sales_POS]([Ser] [int] IDENTITY(1,1) NOT NULL, "
            Sql = Sql & "  	[CostCenterPOS_Id] [int] NULL,"
            Sql = Sql & "  [CostCenterPOS_Name] [nvarchar](150) NULL,"
            Sql = Sql & "  [CostCenter_Id_To] [int] NULL,"
            Sql = Sql & "  [CostCenter_Name_To] [nvarchar](150) NULL,"
            Sql = Sql & "  [Transaction_Code] [int] NULL,"
            Sql = Sql & "  [Product_Id] [int] NULL,	[Product_Code] [nvarchar](50) NULL,"
            Sql = Sql & "  	[Product_Name] [nvarchar](150) NULL,	[Reciving_Q] [float] NULL,"
            Sql = Sql & "  [Cost_Product] [float] NULL,	[CostTotalLine] [float] NULL,"
            Sql = Sql & "  [Patch_Ser] [int] NULL,	[Patch_Name] [nvarchar](150) NULL,"
            Sql = Sql & "  	[Unt_Id] [int] NULL,	[Unt_Name] [nvarchar](50) NULL,"
            Sql = Sql & "  	[Unt_GroupId] [int] NULL,	[Unt_Q] [float] NULL,"
            Sql = Sql & "  [Current_Unt_Id] [int] NULL,	[Current_Unt_Name] [nvarchar](50) NULL,"
            Sql = Sql & "  [Current_Unt_Q] [float] NULL,	[NetQ_Qsetup_CurrentQ] [float] NULL,"
            Sql = Sql & "  [Transaction_Id] [int] NULL,	[Transaction_Date_Create] [datetime] NULL,"
            Sql = Sql & "  [Transaction_Submit] [bit] NULL,	[TransactionDetails_Ser] [int] NULL,"
            Sql = Sql & "  [Authulized] [bit] NULL,	[SOH] [float] NULL,"
            Sql = Sql & "  [Open_Q] [float] NULL,	[Close_Q] [float] NULL,	[Transaction_Patch] [uniqueidentifier] NULL,"
            Sql = Sql & "  [Check_No] [int] NULL,	[TotalAvg] [float] NULL,	[Is_Recipy] [bit] NULL,"
            Sql = Sql & "  [Is_Production] [bit] NULL,	[CompanyPOS_Id] [int] NULL,	[CompanyPOS_Name] [nvarchar](150) NULL,"
            Sql = Sql & "  [OutLetPOS_Id] [int] NULL,	[OutLetPOS_Name] [nvarchar](150) NULL,"
            Sql = Sql & "  [MethodOfPayment_Id] [int] NULL,	[MethodOfPayment_Name] [nvarchar](150) NULL,"
            Sql = Sql & "  	[Sales_Price] [float] NULL,[IsExpire] [bit] NULL,[ProductionCode] [int] NULL,[ChDID] [int] NULL) ON [PRIMARY]"
            Sql = Sql & " ALTER TABLE [dbo].[Sales_POS] ADD  CONSTRAINT [DF_Sales_POS_ChDID]  DEFAULT ((0)) FOR [ChDID] "

            DoneTable = True
        End If

        If TablesCreated.CostCenterLinkPOS = TTables Then
            '  Sql = "SET ANSI_NULLS ON   GO SET QUOTED_IDENTIFIER ON  GO "
            Sql = " CREATE TABLE [CostCenterLinkPOS]("
            Sql = Sql & " [Ser] [int] NULL,"
            Sql = Sql & " 	[CostCenter_Id] [int] NULL,"
            Sql = Sql & " [CostCenter_Name] [nvarchar](150) NULL) ON [PRIMARY]"

            DoneTable = True
        End If

        'If TablesCreate.IFC_Transfer = TTables Then
        '    '  Sql = "SET ANSI_NULLS ON   GO SET QUOTED_IDENTIFIER ON  GO "
        '    Sql = "CREATE VIEW  IFC_Transfer AS "
        '    Sql = Sql & " SELECT     dbo.Transfer_Item.Transfer_No, dbo.Transfer.Date_Submit, SUM(dbo.Transfer_Item.Cost_Ave) AS tot_batch, dbo.Branch_Details.Branch_Name, dbo.Location_Details.Location_Name, "
        '    Sql = Sql & "                   dbo.Transfer.Remark, dbo.Location_Details.MasterAccNo, Branch_Details_1.Branch_Name AS Branch_Name_Recv, Location_Details_1.Location_Name AS Location_Name_Recv, "
        '    Sql = Sql & "                 Location_Details_1.MasterAccNo AS MasterAccNo_Recv"
        '    Sql = Sql & " FROM         dbo.Transfer INNER JOIN"
        '    Sql = Sql & "         dbo.Transfer_Item ON dbo.Transfer.Transfer_No = dbo.Transfer_Item.Transfer_No AND dbo.Transfer.Branch_No = dbo.Transfer_Item.Branch_No INNER JOIN"
        '    Sql = Sql & "         dbo.Location_Details ON dbo.Transfer.Branch_No = dbo.Location_Details.Branch_No AND dbo.Transfer.Location_No = dbo.Location_Details.Location_No INNER JOIN"
        '    Sql = Sql & "         dbo.Location_Details AS Location_Details_1 ON dbo.Transfer.Branch_No_Receiving = Location_Details_1.Branch_No AND "
        '    Sql = Sql & "        dbo.Transfer.Location_No_Receiving = Location_Details_1.Location_No INNER JOIN"
        '    Sql = Sql & "       dbo.Branch_Details ON dbo.Location_Details.Branch_No = dbo.Branch_Details.Branch_No INNER JOIN"
        '    Sql = Sql & "        dbo.Branch_Details AS Branch_Details_1 ON Location_Details_1.Branch_No = Branch_Details_1.Branch_No"
        '    Sql = Sql & " GROUP BY dbo.Transfer_Item.Transfer_No, dbo.Transfer.Remark, dbo.Location_Details.MasterAccNo, dbo.Location_Details.Location_Name, Location_Details_1.Location_Name, "
        '    Sql = Sql & " dbo.Branch_Details.Branch_Name, Branch_Details_1.Branch_Name, Location_Details_1.MasterAccNo, dbo.Transfer.Date_Submit"


        '    DoneTable = True
        'End If

        'If TablesCreate.GardCost_V = TTables Then
        '    '  Sql = "SET ANSI_NULLS ON   GO SET QUOTED_IDENTIFIER ON  GO "
        '    Sql = " CREATE VIEW  GardCost_V AS "
        '    Sql = Sql & " SELECT  Branch, [Branch Cost Centre], [Doc No] + ' - ' + CAST([Date Due] AS nvarchar(10)) + ' - ' + Behaviour AS [Doc No], Behaviour, [Date Due], [Date Submit], [Major Department], [Sub Department], "
        '    Sql = Sql & "                         [Minor Department], Product, Unit, Variance, Positive_Variance AS [Positive Variance], Negative_Variance AS [Negative Variance], Total_Variance AS [Total Variance], Total_Value AS [Total Value], "
        '    Sql = Sql & "            'Stock Take,' + Branch_Doc AS Type_Branch_Doc, BBB, Inventory_Diff, Gl_Code, LLL"
        '    Sql = Sql & " FROM            (SELECT        CAST(BD.Branch_No AS nvarchar(20)) + ' - ' + BD.Branch_Name AS Branch, CAST(BD.Branch_No AS nvarchar(20)) + ' - ' + BD.DocPrefix + ' [' + CAST(LD.Location_No AS nvarchar(20)) "
        '    Sql = Sql & "                                                    + ' - ' + LD.Location_Name + ']' AS [Branch Cost Centre], ISNULL(BD.DocPrefix, '') + RIGHT(REPLACE(CAST(REPLICATE('00000000', 8) AS nvarchar(8)) + CAST(STJ.Doc_No AS nvarchar(10)), '___', "
        '    Sql = Sql & "            '0000000000'), 6) AS [Doc No], ISNULL(ST.Remarks, 'Not Rolled') AS Remarks, CONVERT(NVARCHAR(20), ST.Date_Due, 120) AS [Date Due], CONVERT(NVARCHAR(20), ST.Date_Time, 120) "
        '    Sql = Sql & "                                                AS [Date Submit], ISNULL(STJ.Qty_Closing - STJ.Qty_Opening, 0) AS Variance, ISNULL(CASE WHEN Qty_Opening < Qty_Closing THEN (Qty_Closing - Qty_Opening) * STJ.Cost_Average ELSE 0 END, "
        '    Sql = Sql & "                                           0) AS Positive_Variance, ISNULL(CASE WHEN Qty_Opening > Qty_Closing THEN (Qty_Opening - Qty_Closing) * STJ.Cost_Average ELSE 0 END, 0) AS Negative_Variance, "
        '    Sql = Sql & "                                          (CASE WHEN upper(isnull(st.Behaviour, 'SYSTEM')) = 'SYSTEM' THEN 'Default System' ELSE st.Behaviour END) AS Behaviour, ISNULL((STJ.Qty_Closing - STJ.Qty_Opening) * STJ.Cost_Average, 0) "
        '    Sql = Sql & "                                         AS Total_Variance, ISNULL(STJ.Qty_Closing * STJ.Cost_Average, 0) AS Total_Value, PD.Description, DDMajor.Department_No AS MajorNo, DDMajor.Department_Name AS MajorName, "
        '    Sql = Sql & "                                        DDSub.Department_No AS SubNo, DDSub.Department_Name AS SubName, DDMinor.Department_No AS MinorNo, DDMinor.Department_Name AS MinorName, "
        '    Sql = Sql & "                                        CAST(DDMajor.Department_No AS nvarchar(20)) + ' - ' + DDMajor.Department_Name AS [Major Department], CAST(DDSub.Department_No AS nvarchar(20)) "
        '    Sql = Sql & "                                       + ' - ' + DDSub.Department_Name AS [Sub Department], CAST(DDMinor.Department_No AS nvarchar(20)) + ' "
        '    Sql = Sql & " - ' + DDMinor.Department_Name AS [Minor Department], PD.Product_Code, "
        '    Sql = Sql & "                                                 CAST(STJ.Product_Code AS nvarchar(50)) + ' - ' + CASE WHEN ISNULL(PD.Brand_Name, 'No Brand') = 'No Brand' THEN ISNULL(PD.[Description], '') ELSE ISNULL(PD.Brand_Name, '') "
        '    Sql = Sql & "                                         + ' ' + PD.[Description] END AS Product, CASE WHEN RTRIM(CAST(PD.Unit_Size AS nvarchar)) = '' THEN '' ELSE CASE WHEN PD.Unit_Size = 0 THEN '' ELSE RTRIM(CAST(PD.Unit_Size AS nvarchar)) "
        '    Sql = Sql & "                                         END END + ' ' + ISNULL(PD.Unit_of_Measure, 0) AS Unit, CAST(STJ.Branch_No AS nvarchar(20)) + '-' + CAST(STJ.Location_No AS nvarchar(20)) + ',' + CAST(STJ.Doc_No AS nvarchar(20)) "
        '    Sql = Sql & "                                        AS Branch_Doc, BD.Branch_No AS BBB, LD.Location_No AS LLL, DDMajor.Inventory_Diff, DDMinor.Gl_Code"
        '    Sql = Sql & "               FROM            dbo.Stock_Takes AS ST WITH (NOLOCK) INNER JOIN"
        '    Sql = Sql & "                                       dbo.Stock_Take_Journal AS STJ WITH (NOLOCK) ON ST.Branch_No = STJ.Branch_No AND ST.Location_No = STJ.Location_No AND ST.Take_No = STJ.Doc_No INNER JOIN"
        '    Sql = Sql & "                                      dbo.Branch_Details AS BD WITH (NOLOCK) ON ST.Branch_No = BD.Branch_No INNER JOIN"
        '    Sql = Sql & "                                      dbo.Location_Details AS LD WITH (NOLOCK) ON ST.Branch_No = LD.Branch_No AND ST.Location_No = LD.Location_No INNER JOIN"
        '    Sql = Sql & "                                      dbo.Product_Details AS PD WITH (NOLOCK) ON STJ.Branch_No = PD.Branch_No AND STJ.Product_Code = PD.Product_Code LEFT OUTER JOIN"
        '    Sql = Sql & "                        dbo.Department_Details AS DDMinor WITH (NOLOCK) ON ST.Branch_No = DDMinor.Branch_No AND PD.Department_No = DDMinor.Department_No LEFT OUTER JOIN"
        '    Sql = Sql & "                           dbo.Department_Details AS DDSub WITH (NOLOCK) ON DDSub.Branch_No = DDMinor.Branch_No AND DDSub.Master = DDMinor.Master AND DDSub.Sub = DDMinor.Sub AND DDSub.Minor IS NULL "
        '    Sql = Sql & "             LEFT OUTER JOIN"
        '    Sql = Sql & " dbo.Department_Details AS DDMajor WITH (NOLOCK) ON DDMajor.Branch_No = DDMinor.Branch_No AND DDMajor.Master = DDMinor.Master AND DDMajor.Sub IS NULL AND "
        '    Sql = Sql & "                                     DDMajor.Minor IS NULL) AS derivedtbl_1"



        '    DoneTable = True
        'End If

        If DoneTable Then
            Dim cmd As New SqlCommand

            'If Cn.State = ConnectionState.Open Then Cn.Close()
            ' Cn.Open()
            Conn.OpenConnection()
            cmd.CommandText = Sql '"select * from Inventory_Period where Branch_No=1 and Y_Period=2017   order by Period_No"
            cmd.Connection = Conn.SqlConn
            cmd.ExecuteNonQuery()
            Conn.CloseConnection()

        End If
    End Sub

    Public Function TablesHAsData(TableName As String) As Integer
        Dim Dt As New DataTable
        Dt.Clear()
        Dt = Conn.SELECT_TXT("Select * From " & TableName)

        Return Dt.Rows.Count
    End Function

    Private Sub Btn_Login_Click(sender As Object, e As EventArgs) Handles Btn_Login.Click
        Dim F As New AdminPass_Frm
        F.LblTitle.Text = "Enter Password Login To " & Btn_Login.Text
        F.ShowDialog()

        If F.IsSelect Then
            If F.Txt_Pass.Text = Conn.AdminPass Then
                Conn.CallLoginPOS(False)
            End If

        End If

    End Sub

    Private Sub Btn_Setup_Click(sender As Object, e As EventArgs) Handles Btn_Setup.Click
        Dim F As New AdminPass_Frm
        F.LblTitle.Text = "Enter Password Login To " & Btn_Setup.Text
        F.ShowDialog()

        If F.IsSelect Then
            If F.Txt_Pass.Text = Conn.AdminPass Then
                Dim S As New Setting_Frm
                S.ShowDialog()

            End If

        End If

    End Sub

    Public Function ClosedDay() As Boolean
        Dim Closed As Boolean = False
        Dim Dt As New DataTable
        Dt.Clear()
        Dim Dtfrm, DtToo As String
        Dtfrm = DPFromDate.Value.ToString("yyyy/MM/dd 00:00:00")
        DtToo = DPFromDate.Value.ToString("yyyy/MM/dd 23:59:59")

        Dt = Conn.SELECT_TXTPOS("select * from SystemDate where (SysDate >= CONVERT(DATETIME, '" & Dtfrm & "', 102)) AND (SysDate <= CONVERT(DATETIME,'" & DtToo & "', 102)) and WaiterID>0 and EndDate is not null  ")
        If Dt.Rows.Count > 0 Then
            Closed = True
        End If
        Return Closed
    End Function
    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Try
            Dim Invalid As Boolean
            Dim Dt As New DataTable
            Dt.Clear()
            Dim DpFrom, DpTo As New DateTime
            DpToDate.Value = DPFromDate.Value
            If Val(Comp_CostCenter.SelectedValue) <= 0 Then
                MessageBox.Show("Please Choose Costcenter", "SCM")
                Comp_CostCenter.Focus()
                Return
            End If
            If IsManuel Then
                For r As Integer = 0 To DGV.Rows.Count - 1
                    'If IsDBNull(Dt.Rows(r)("Date")) Then
                    DGV(r, "statistdate") = DPFromDate.Value  'Dt.Rows(r)("Date")
                    'Else
                    '    DGV(DGV.Rows.Count - 1, "statistdate") = Dt.Rows(r)("Date")
                    'End If
                Next
            End If
            IFC_Cls.DeleteQuantityNow()
            IsProduction = False
            Dt = IFC_Cls.GetLasttrans_date(DPFromDate.Value)
            If Dt.Rows.Count > 0 Then
                DpLess.Value = Format(Convert.ToDateTime(Dt.Rows(0)("Transaction_Date_Create")), "yyyy/MM/dd HH:mm:ss")

            Else
                DpLess.Value = New Date(Date.Now.Year - 1, Date.Now.Month, Date.Now.Day)
                'DpLess.Value = DpLess.Value.Year - 1

            End If
            DpNow.Value = New Date(Date.Now.Year, Date.Now.Month, Date.Now.Day)

            DpFrom = Format(DPFromDate.Value, "yyyy/MM/dd")
            DpTo = Format(DpToDate.Value, "yyyy/MM/dd")
            '///////////////////////////////////////////// If POS Smart ////////////////////////
            If DpFrom <= DpLess.Value Then
                MessageBox.Show("Sorry Must From Date More Than  " & DpLess.Value.ToString(), "IFC SCM POS", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            If Conn.POS = "Smart POS" Then
                If ClosedDay() = False Then
                    MessageBox.Show("Sorry Smart Pos Not Day Turn  Over At This Date " & DPFromDate.Value.ToShortDateString(), "IFC SCM POS", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Return
                End If

            Else
                '///////////////////////////////////////////// If POS Matrix ////////////////////////



                If DpTo >= DpNow.Value Then
                    MessageBox.Show("Sorry Must From Date Less Than  " & DpNow.Value.ToString(), "IFC SCM POS", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Return
                End If

                If DPFromDate.Value > DpToDate.Value Then
                    MessageBox.Show("Sorry Must From Date Less Than To Date ", "IFC SCM POS", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Return
                End If


            End If

            If IsManuel Then
                'LoadPOSDataManuale(False)
            Else
                LoadPOSData(False)
            End If


            If DGV.Rows.Count > 1 Then
                Dim DtCosLink As New DataTable
                DtCosLink.Clear()
                DtCosLink = ClsSetting.GetCostDataLink()

                For R As Integer = 0 To DtCosLink.Rows.Count - 1
                    Invalid = IFC_Cls.CheckValiDate(Val(DtCosLink.Rows(R)("CostCenter_Id")), DPFromDate.Value, DtCosLink.Rows(R)("CostCenter_Name"))

                    If Invalid = False Then Exit For

                    Invalid = IFC_Cls.CheckValiDate(Val(DtCosLink.Rows(R)("CostCenter_Id")), DpToDate.Value, DtCosLink.Rows(R)("CostCenter_Name"))

                    If Invalid = False Then Exit For
                Next

                If Invalid = False Then Return
                'Dim ValueV As Boolean
                ''/////////////////// Check Period Inventory////////////////////////
                'ValueV = IFC_Cls.CheckValiDate(Val(DtCostcenter.Rows(0)("CostCenter_Id")), Convert.ToDateTime(DGV(Pross, "statistdate")), DtCostcenter.Rows(0)("CostCenter_Name"))
                'If ValueV = False Then
                '    DGV.Rows(Pross).Selected = True
                '    DGvSCM.DataSource = Nothing
                '    F.Close()
                '    MessageBox.Show("Sorry The Perriod Inventory is Closed With This Costcenter " & DtCostcenter.Rows(0)("CostCenter_Name").ToString(), Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Error)
                '    Return False
                'End If
                Conn.EXECUT_Txt("update Patches set usd=0 where CloseOpen=0")
                Invalid = CheckTransActionsSales()



                If Invalid = False Then
                    Conn.EXECUT_Txt("update Patches set usd=0 where CloseOpen=0")
                    Frm_Progress.Hide()

                    Return
                Else
                    SaveTransActionsSales()
                End If
            Else
                Conn.EXECUT_Txt("update Patches set usd=0 where CloseOpen=0")
                MessageBox.Show("No Tansactions Found", "IFC SCM POS", MessageBoxButtons.OK, MessageBoxIcon.Stop)
                Return
            End If
            Conn.EXECUT_Txt("update Patches set usd=0 where CloseOpen=0")
            MessageBox.Show("Complete Transaction Sales")

        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub
    Public Function CheckTransActionsSalesOnLine() As Boolean
        ' On Error Resume Next
        Dim IsProductionMy As Boolean = False
        Dim returnV As Boolean = True
        DGvSCM.DataSource = Nothing
        Application.DoEvents()
        DGvSCM.DataSource = IFC_Cls.SalePosLoadInDGVSCM()
        Dim CostAv, TotalAv, OpenAv, CloseAv, ExpQ, NetQ_Qsetup_CurrentQ, QuantityNow As Decimal
        Dim DtSetting, Dtproduct, DtSOH, DtExpired, DtCostcenter As New DataTable
        Dim patchSer As Integer
        Dim Patchname As String
        Dim QCu As Double = 0
        Application.DoEvents()
        Dim Prosstage As Integer = 0
        Dim tot = 100
        Dim F As New Frm_Progress
        F.Show()
        For Pross = 1 To DGV.Rows.Count - 1

            Prosstage = ((Pross) / (DGV.Rows.Count - 1)) * 100
            F.LblStatus.Text = String.Format("Check Data validation Prossesing..{0}%", Prosstage)
            If Pross > 100 Or Pross < 0 Then F.ProgressBar1.Value = Pross
            F.ProgressBar1.Update()
            F.ProgressBar1.Visible = True
            Thread.Sleep(10)
            DtSetting.Clear()
            DGV.Rows(Pross).Selected = False

            '///////////////////// Load POs Setting
            DtSetting = IFC_Cls.GetSettingPOS(Val(DGV(Pross, "mandant")), Val(DGV(Pross, "outlet")), Val(DGV(Pross, "center")))

            If DtSetting.Rows.Count = 0 Then
                DGV.Rows(Pross).Selected = True
                DGvSCM.DataSource = Nothing
                F.Close()
                MessageBox.Show("Not Found Company Or Outlet For costCenter " + DGV(Pross, "centername").ToString(), Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Error)
                F.Close()
                Return False
            End If

            '////////////////Load Product Data
            'If DGV.Rows(Pross)("plu").ToString() = "10106" Then
            '    MessageBox.Show("OK")
            'End If
            Dtproduct.Clear()
            Dtproduct = IFC_Cls.GetProductData(DGV.Rows(Pross)("plu").ToString())
            If Dtproduct.Rows.Count = 0 Then
                DGV.Rows(Pross).Selected = True
                DGvSCM.DataSource = Nothing
                F.Close()
                MessageBox.Show(" This Product Code " + DGV(Pross, "plu").ToString() + " Not found In sales Product", Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Error)
                F.Close()
                Return False
            End If
            '///////////////// Load SOH Product ///////////////////////////////////////////
            Dim Quer, loopNo As String
            DtCostcenter.Clear()
            DtCostcenter = IFC_Cls.GetCostCenterData(Val(DtSetting.Rows(0)("Ser")))

            If DtCostcenter.Rows.Count = 0 Then
                DGV.Rows(Pross).Selected = True
                DGvSCM.DataSource = Nothing
                F.Close()
                MessageBox.Show("Sorry This Product Not have In This Store " & DtSetting.Rows(0)("Store_NameSCM").ToString(), Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Error)
                F.Close()
                Return False

            End If
            loopNo = ""
            For t As Integer = 0 To DtCostcenter.Rows.Count - 1
                If t = 0 Then
                    loopNo = DtCostcenter.Rows(t)("CostCenter_Id").ToString()
                Else
                    loopNo += "," + DtCostcenter.Rows(t)("CostCenter_Id").ToString()
                End If
            Next
            Quer = " and CostCenter_Id in (" + loopNo + ")"

            '//////////////////////////////////////////////////
            DtSOH.Clear()
            DtSOH = IFC_Cls.GetSOHDataGetting(Val(Dtproduct.Rows(0)("Product_Id")), Quer.ToString())  ', Val(DtSOH.Rows(0)("CostCenter_Id"))) ', Val(DtSetting.Rows(0)("CostCenter_IdPOS")))

            If DtSOH.Rows.Count = 0 Then
                DGV.Rows(Pross).Selected = True
                DGvSCM.DataSource = Nothing
                F.Close()
                MessageBox.Show("This Product  " + Dtproduct.Rows(0)("Product_Name").ToString() + " Not Linked With CostCenter " + DtSetting.Rows(0)("CostCenter_NamePOS").ToString(), Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Error)
                F.Close()
                Return False
            End If

            DtCostcenter.Clear()
            DtCostcenter = IFC_Cls.GetCostCenterDataTrue(Val(DtSOH.Rows(0)("CostCenter_Id")))

            If DtCostcenter.Rows.Count = 0 Then
                DGV.Rows(Pross).Selected = True
                DGvSCM.DataSource = Nothing
                F.Close()
                MessageBox.Show("Sorry This Product Not have In This Store " & DtSetting.Rows(0)("Store_NameSCM").ToString(), Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Error)
                F.Close()
                Return False

            End If
            CostAv = 0
            QuantityNow = 0
            TotalAv = 0
            OpenAv = 0
            CloseAv = 0
            ExpQ = 0
            NetQ_Qsetup_CurrentQ = 0
            '
            Dim ValueV As Boolean
            '/////////////////// Check Period Inventory////////////////////////
            ValueV = IFC_Cls.CheckValiDate(Val(DtCostcenter.Rows(0)("CostCenter_Id")), Convert.ToDateTime(DGV(Pross, "statistdate")), DtCostcenter.Rows(0)("CostCenter_Name"))
            If ValueV = False Then
                DGV.Rows(Pross).Selected = True
                DGvSCM.DataSource = Nothing
                F.Close()
                MessageBox.Show("Sorry The Perriod Inventory is Closed With This Costcenter " & DtCostcenter.Rows(0)("CostCenter_Name").ToString(), Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Error)
                F.Close()
                Return False
            End If
            'DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Id") = Val(Dtproduct.Rows(0)("Unt_IdSales"))
            'DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Name") = Dtproduct.Rows(0)("Unt_NameSales")
            'DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Q") = Dtproduct.Rows(0)("Unt_QSales")
            'DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = QCu * Dtproduct.Rows(0)("Unt_QSales")
            'DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") / Dtproduct.Rows(0)("Unt_Q") 'QCu '* -1 ' Convert.ToDouble(DGV(Pross, "Qty"))
            'DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") * -1

            CostAv = Dtproduct.Rows(0)("Unt_QSales") / Dtproduct.Rows(0)("Unt_Q")
            CostAv = CostAv * DtSOH.Rows(0)("AvCost")

            NetQ_Qsetup_CurrentQ = Dtproduct.Rows(0)("Unt_QSales") / Dtproduct.Rows(0)("Unt_Q")
            NetQ_Qsetup_CurrentQ = NetQ_Qsetup_CurrentQ * DGV(Pross, "Qty") 'DtSOH.Rows(0)("AvCost")
            'Convert.ToDouble(DGV(Pross, "Qty"))
            TotalAv = NetQ_Qsetup_CurrentQ * CostAv ' DtSOH.Rows(0)("AvCost")
            OpenAv = DtSOH.Rows(0)("Quntity")

            QuantityNow = IFC_Cls.GetQuantityNow(CInt(DtSOH.Rows(0)("Product_Id")), CInt(DtSOH.Rows(0)("CostCenter_Id")), DtSOH.Rows(0)("Product_Code").ToString(), NetQ_Qsetup_CurrentQ)
            CloseAv = DtSOH.Rows(0)("Quntity") - (NetQ_Qsetup_CurrentQ + QuantityNow) ' Convert.ToDouble(DGV(Pross, "Qty"))


            '////////////////////////check soh \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\

            If Convert.ToBoolean(Dtproduct.Rows(0)("IsExpire")) = True Then

                DtSOH.Clear()
                DtSOH = IFC_Cls.GetSOHData(Val(Dtproduct.Rows(0)("Product_Id")), Val(DtCostcenter.Rows(0)("CostCenter_Id"))) ', Val(DtSetting.Rows(0)("CostCenter_IdPOS")))
                'DGV(Pross, "Qty")
                If DtSOH.Rows.Count = 0 Then
                    returnV = False
                    MessageBox.Show("Sorry The Product Code " & Dtproduct.Rows(0)("Product_Code") & " Not Link with Costcenter Name " & DtCostcenter.Rows(0)("CostCenter_Name"))
                    F.Close()
                    Return returnV

                Else
                    If NetQ_Qsetup_CurrentQ > (CDec(DtSOH.Rows(0)("Quntity")) + QuantityNow) Then
                        returnV = False
                        MessageBox.Show("Sorry The Product Code " & Dtproduct.Rows(0)("Product_Code") & " Not Enough Quantity with Costcenter Name " & DtCostcenter.Rows(0)("CostCenter_Name"))
                        F.Close()
                        Return returnV
                    End If
                End If

                DtExpired.Clear()
                DtExpired = IFC_Cls.GetExpiredData(Val(Dtproduct.Rows(0)("Product_Id")), Val(DtSOH.Rows(0)("CostCenter_Id")))
                If DtExpired.Rows.Count > 0 Then
                    patchSer = Val(DtExpired.Rows(0)("Patch_Ser"))
                    Patchname = DtExpired.Rows(0)("Patch_Name")

                    If NetQ_Qsetup_CurrentQ > DtExpired.Rows(0)("NetQ_Qsetup_CurrentQ") Then
                        Dim R As Integer = 0
                        Dim QQ As Double = 0

                        Dim Deff As Decimal
                        For Deff = 0 To NetQ_Qsetup_CurrentQ 'Convert.ToDouble(DGV(Pross, "Qty"))
y:
                            DtSOH.Clear()
                            DtSOH = IFC_Cls.GetSOHData(Val(Dtproduct.Rows(0)("Product_Id")), Val(DtCostcenter.Rows(0)("CostCenter_Id"))) ', Val(DtSetting.Rows(0)("CostCenter_IdPOS")))
                            'DGV(Pross, "Qty")
                            If R = 0 Then
                                QQ = NetQ_Qsetup_CurrentQ - DtExpired.Rows(0)("NetQ_Qsetup_CurrentQ")
                                Deff += DtExpired.Rows(0)("NetQ_Qsetup_CurrentQ")
                                QCu = DtExpired.Rows(0)("NetQ_Qsetup_CurrentQ")
                                IFC_Cls.UpdateUsePatch(patchSer, Val(Dtproduct.Rows(0)("Product_Id")), Val(DtSOH.Rows(0)("CostCenter_Id")))
                            Else


                                DtExpired.Clear()
                                DtExpired = IFC_Cls.GetExpiredData(Val(Dtproduct.Rows(0)("Product_Id")), Val(DtSOH.Rows(0)("CostCenter_Id")))
                                If DtExpired.Rows.Count > 0 Then
                                    If QQ > Convert.ToDouble(DtExpired.Rows(0)("NetQ_Qsetup_CurrentQ")) Then
                                        QQ = QQ - DtExpired.Rows(0)("NetQ_Qsetup_CurrentQ")
                                        Deff += DtExpired.Rows(0)("NetQ_Qsetup_CurrentQ")
                                        QCu = DtExpired.Rows(0)("NetQ_Qsetup_CurrentQ")
                                        patchSer = Val(DtExpired.Rows(0)("Patch_Ser"))
                                        Patchname = DtExpired.Rows(0)("Patch_Name")
                                    Else
                                        Deff += QQ
                                        QCu = QQ
                                        patchSer = 0
                                        Patchname = ""

                                    End If
                                    patchSer = Val(DtExpired.Rows(0)("Patch_Ser"))
                                    Patchname = DtExpired.Rows(0)("Patch_Name")
                                    IFC_Cls.UpdateUsePatch(patchSer, Val(Dtproduct.Rows(0)("Product_Id")), Val(DtSOH.Rows(0)("CostCenter_Id")))
                                Else
                                    Deff += QQ
                                    QCu = QQ
                                    patchSer = 0
                                    Patchname = ""
                                End If


                            End If

                            '  CostAv = DtSOH.Rows(0)("AvCost")
                            CostAv = Dtproduct.Rows(0)("Unt_QSales") / Dtproduct.Rows(0)("Unt_Q")
                            CostAv = CostAv * DtSOH.Rows(0)("AvCost")

                            TotalAv = QCu * CostAv ' DtSOH.Rows(0)("AvCost")
                            OpenAv = DtSOH.Rows(0)("Quntity")
                            CloseAv = DtSOH.Rows(0)("Quntity") - QCu 'Convert.ToDouble(DGV(Pross, "Qty"))


                            'CostAv = DtSOH.Rows(0)("AvCost")
                            'TotalAv = Convert.ToDouble(DGV(Pross, "Qty")) * DtSOH.Rows(0)("AvCost")
                            'OpenAv = DtSOH.Rows(0)("Quntity")
                            'CloseAv = Convert.ToDouble(DGV(Pross, "Qty")) - DtSOH.Rows(0)("Quntity")

                            DGvSCM.Rows.Add()
                            DGvSCM(DGvSCM.Rows.Count - 1, "CompanyPOS_Id") = Val(DGV(Pross, "mandant"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "CompanyPOS_Name") = DtSetting.Rows(0)("Company_NamePOS")
                            DGvSCM(DGvSCM.Rows.Count - 1, "OutLetPOS_Id") = Val(DGV(Pross, "outlet"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "OutLetPOS_Name") = DtSetting.Rows(0)("Brand_NamePOS")
                            DGvSCM(DGvSCM.Rows.Count - 1, "CostCenterPOS_Id") = Val(DGV(Pross, "center"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "CostCenterPOS_Name") = DGV(Pross, "centername")
                            DGvSCM(DGvSCM.Rows.Count - 1, "CostCenter_Id_To") = Val(DtCostcenter.Rows(0)("CostCenter_Id"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "CostCenter_Name_To") = DtCostcenter.Rows(0)("CostCenter_Name")
                            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Code") = 0
                            DGvSCM(DGvSCM.Rows.Count - 1, "Product_Id") = Val(Dtproduct.Rows(0)("Product_Id"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "Product_Code") = DGV(Pross, "plu")
                            DGvSCM(DGvSCM.Rows.Count - 1, "Product_Name") = Dtproduct.Rows(0)("Product_Name")
                            DGvSCM(DGvSCM.Rows.Count - 1, "Reciving_Q") = (Dtproduct.Rows(0)("Unt_Q") / Dtproduct.Rows(0)("Unt_QSales")) * (QCu * -1) 'Convert.ToDouble(DGV(Pross, "Qty")) * Val(-1) 'QCu * -1 '
                            DGvSCM(DGvSCM.Rows.Count - 1, "Cost_Product") = CostAv
                            DGvSCM(DGvSCM.Rows.Count - 1, "CostTotalLine") = DGV(Pross, "price") * QCu ' DGV(Pross, "Amount")
                            DGvSCM(DGvSCM.Rows.Count - 1, "Patch_Ser") = patchSer
                            DGvSCM(DGvSCM.Rows.Count - 1, "Patch_Name") = Patchname
                            DGvSCM(DGvSCM.Rows.Count - 1, "Unt_Id") = Val(Dtproduct.Rows(0)("Unt_Id"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "Unt_Name") = Dtproduct.Rows(0)("Unt_Name")
                            DGvSCM(DGvSCM.Rows.Count - 1, "Unt_GroupId") = Dtproduct.Rows(0)("Unt_GroupId")
                            DGvSCM(DGvSCM.Rows.Count - 1, "Unt_Q") = Dtproduct.Rows(0)("Unt_Q")

                            DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Id") = Val(Dtproduct.Rows(0)("Unt_IdSales"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Name") = Dtproduct.Rows(0)("Unt_NameSales")
                            DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Q") = Dtproduct.Rows(0)("Unt_QSales")
                            DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = QCu * -1 '* Dtproduct.Rows(0)("Unt_QSales")
                            'DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") / Dtproduct.Rows(0)("Unt_Q") 'QCu '* -1 ' Convert.ToDouble(DGV(Pross, "Qty"))
                            'DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") * -1

                            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Id") = 0
                            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Date_Create") = Convert.ToDateTime(DGV(Pross, "statistdate"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Submit") = Convert.ToBoolean(True)
                            DGvSCM(DGvSCM.Rows.Count - 1, "TransactionDetails_Ser") = ""
                            DGvSCM(DGvSCM.Rows.Count - 1, "Authulized") = Convert.ToBoolean(True)
                            DGvSCM(DGvSCM.Rows.Count - 1, "SOH") = CloseAv
                            DGvSCM(DGvSCM.Rows.Count - 1, "Open_Q") = OpenAv
                            DGvSCM(DGvSCM.Rows.Count - 1, "Close_Q") = CloseAv
                            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Patch") = ""
                            DGvSCM(DGvSCM.Rows.Count - 1, "Check_No") = DGV(Pross, "billnum")
                            DGvSCM(DGvSCM.Rows.Count - 1, "TotalAvg") = QCu * CostAv * Val(-1)
                            DGvSCM(DGvSCM.Rows.Count - 1, "Is_Recipy") = Convert.ToBoolean(Dtproduct.Rows(0)("IsRecipe"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "Is_Production") = Convert.ToBoolean(Dtproduct.Rows(0)("IsProduction"))

                            DGvSCM(DGvSCM.Rows.Count - 1, "MethodOfPayment_Id") = Val(DGV(Pross, "payform"))

                            DGvSCM(DGvSCM.Rows.Count - 1, "MethodOfPayment_Name") = DGV(Pross, "payformname")
                            DGvSCM(DGvSCM.Rows.Count - 1, "Sales_Price") = DGV(Pross, "price")
                            DGvSCM(DGvSCM.Rows.Count - 1, "IsExpire") = Convert.ToBoolean(Dtproduct.Rows(0)("IsExpire"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "ProductionCode") = 0 ' ""
                            'MessageBox.Show(DGV(Pross, "ChDID"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "ChDID") = Val(DGV(Pross, "ChDID"))
                            IsProductionMy = False
                            If Convert.ToBoolean(Dtproduct.Rows(0)("IsProduction")) = True Then
                                IsProduction = True
                                IsProductionMy = True
                            End If

                            R += 1
                            If Deff < NetQ_Qsetup_CurrentQ Then GoTo y
                        Next

                        GoTo E
                    End If

                Else
                    patchSer = 0
                    Patchname = ""
                End If

            Else
                patchSer = 0
                Patchname = ""
            End If

            If Convert.ToBoolean(Dtproduct.Rows(0)("IsExpire")) = True And patchSer > 0 Then
                IFC_Cls.UpdateUsePatch(patchSer, Val(Dtproduct.Rows(0)("Product_Id")), Val(DtSOH.Rows(0)("CostCenter_Id")))
            End If


            '/////////////////////////////////////////////// Normal No Expired No production ////////////////////////////////////////////
            DGvSCM.Rows.Add()
            DGvSCM(DGvSCM.Rows.Count - 1, "CompanyPOS_Id") = Val(DGV(Pross, "mandant"))
            DGvSCM(DGvSCM.Rows.Count - 1, "CompanyPOS_Name") = DtSetting.Rows(0)("Company_NamePOS")
            DGvSCM(DGvSCM.Rows.Count - 1, "OutLetPOS_Id") = Val(DGV(Pross, "outlet"))
            DGvSCM(DGvSCM.Rows.Count - 1, "OutLetPOS_Name") = DtSetting.Rows(0)("Brand_NamePOS")
            DGvSCM(DGvSCM.Rows.Count - 1, "CostCenterPOS_Id") = Val(DGV(Pross, "center"))
            DGvSCM(DGvSCM.Rows.Count - 1, "CostCenterPOS_Name") = DGV(Pross, "centername")
            DGvSCM(DGvSCM.Rows.Count - 1, "CostCenter_Id_To") = Val(DtCostcenter.Rows(0)("CostCenter_Id"))
            DGvSCM(DGvSCM.Rows.Count - 1, "CostCenter_Name_To") = DtCostcenter.Rows(0)("CostCenter_Name")
            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Code") = 0
            DGvSCM(DGvSCM.Rows.Count - 1, "Product_Id") = Val(Dtproduct.Rows(0)("Product_Id"))
            DGvSCM(DGvSCM.Rows.Count - 1, "Product_Code") = DGV(Pross, "plu")
            DGvSCM(DGvSCM.Rows.Count - 1, "Product_Name") = Dtproduct.Rows(0)("Product_Name")
            DGvSCM(DGvSCM.Rows.Count - 1, "Reciving_Q") = (NetQ_Qsetup_CurrentQ * Dtproduct.Rows(0)("Unt_Q") * Val(-1)) / Dtproduct.Rows(0)("Unt_QSales") 'Convert.ToDouble(DGV(Pross, "Qty"))
            DGvSCM(DGvSCM.Rows.Count - 1, "Cost_Product") = CostAv
            DGvSCM(DGvSCM.Rows.Count - 1, "CostTotalLine") = DGV(Pross, "Amount")
            DGvSCM(DGvSCM.Rows.Count - 1, "Patch_Ser") = patchSer
            DGvSCM(DGvSCM.Rows.Count - 1, "Patch_Name") = Patchname
            DGvSCM(DGvSCM.Rows.Count - 1, "Unt_Id") = Val(Dtproduct.Rows(0)("Unt_Id"))
            DGvSCM(DGvSCM.Rows.Count - 1, "Unt_Name") = Dtproduct.Rows(0)("Unt_Name")
            DGvSCM(DGvSCM.Rows.Count - 1, "Unt_GroupId") = Dtproduct.Rows(0)("Unt_GroupId")
            DGvSCM(DGvSCM.Rows.Count - 1, "Unt_Q") = Dtproduct.Rows(0)("Unt_Q")
            'DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Id") = Val(Dtproduct.Rows(0)("Unt_Id"))
            'DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Name") = Dtproduct.Rows(0)("Unt_Name")
            'DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Q") = Dtproduct.Rows(0)("Unt_GroupId")
            'DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = Convert.ToDouble(DGV(Pross, "Qty"))

            DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Id") = Val(Dtproduct.Rows(0)("Unt_IdSales"))
            DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Name") = Dtproduct.Rows(0)("Unt_NameSales")
            DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Q") = Dtproduct.Rows(0)("Unt_QSales")
            DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = DGV(Pross, "Qty") * Dtproduct.Rows(0)("Unt_QSales")
            DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") / Dtproduct.Rows(0)("Unt_Q") 'QCu '* -1 ' Convert.ToDouble(DGV(Pross, "Qty"))
            DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") * -1

            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Id") = 0
            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Date_Create") = Convert.ToDateTime(DGV(Pross, "statistdate"))
            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Submit") = Convert.ToBoolean(True)
            DGvSCM(DGvSCM.Rows.Count - 1, "TransactionDetails_Ser") = ""
            DGvSCM(DGvSCM.Rows.Count - 1, "Authulized") = Convert.ToBoolean(True)
            DGvSCM(DGvSCM.Rows.Count - 1, "SOH") = CloseAv
            DGvSCM(DGvSCM.Rows.Count - 1, "Open_Q") = OpenAv
            DGvSCM(DGvSCM.Rows.Count - 1, "Close_Q") = CloseAv
            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Patch") = ""
            'DGvSCM(DGvSCM.Rows.Count - 1, "Check_No") = DGV(Pross, "billnum")
            DGvSCM(DGvSCM.Rows.Count - 1, "TotalAvg") = TotalAv * Val(-1)
            DGvSCM(DGvSCM.Rows.Count - 1, "Is_Recipy") = Convert.ToBoolean(Dtproduct.Rows(0)("IsRecipe"))
            DGvSCM(DGvSCM.Rows.Count - 1, "Is_Production") = Convert.ToBoolean(Dtproduct.Rows(0)("IsProduction"))

            'DGvSCM(DGvSCM.Rows.Count - 1, "MethodOfPayment_Id") = Val(DGV(Pross, "payform"))
            'DGvSCM(DGvSCM.Rows.Count - 1, "MethodOfPayment_Name") = DGV(Pross, "payformname")
            DGvSCM(DGvSCM.Rows.Count - 1, "Sales_Price") = DGV(Pross, "price")
            DGvSCM(DGvSCM.Rows.Count - 1, "IsExpire") = Convert.ToBoolean(Dtproduct.Rows(0)("IsExpire"))
            DGvSCM(DGvSCM.Rows.Count - 1, "ProductionCode") = 0 ' ""
            'MessageBox.Show(DGV(Pross, "ChDID"))
            DGvSCM(DGvSCM.Rows.Count - 1, "ChDID") = Val(DGV(Pross, "ChDID"))
            ' DGvSCM(DGvSCM.Rows.Count - 1, "") = ""
            IsProductionMy = False
            If Convert.ToBoolean(Dtproduct.Rows(0)("IsProduction")) = True Then
                IsProduction = True
                IsProductionMy = True
            End If

            returnV = True
            If CloseAv < 0 And IsProductionMy = False Then
                returnV = False
                MessageBox.Show("Sorry The Quantity Of This Product : " + DGvSCM(DGvSCM.Rows.Count - 1, "Product_Code").ToString() + " / " + DGvSCM(DGvSCM.Rows.Count - 1, "Product_Name").ToString() + " No Quantity Enough In CostCenter : " & DtCostcenter.Rows(0)("CostCenter_Name").ToString())
                Exit For
            End If

            If IsProductionMy Then
                Grids_Bulid_prod()
                DGVProduction.Rows(1).Selected = True

                GetProductionRecipyData(DGvSCM.Rows.Count - 1)
                Dim SubM As Boolean = RowEffectQuantity(DGvSCM.Rows.Count - 1, True)
                If SubM = False Then
                    MessageBox.Show("Sorry The Quantity Of This Product : " + DGvSCM(DGvSCM.Rows.Count - 1, "Product_Code").ToString() + " / " + DGvSCM(DGvSCM.Rows.Count - 1, "Product_Name").ToString() + " No Quantity Enough In CostCenter : " & DtCostcenter.Rows(0)("CostCenter_Name").ToString())
                    returnV = False
                    Exit For
                End If
            End If

E:
        Next
        ' F.Close()




        F.Close()
        Return returnV
    End Function
    Public Function CheckTransActionsSales() As Boolean
        On Error Resume Next
        Dim IsProductionMy As Boolean = False
        Dim returnV As Boolean = True
        DGvSCM.DataSource = Nothing

        Application.DoEvents()
        DGvSCM.DataSource = IFC_Cls.SalePosLoadInDGVSCM()
        Dim CostAv, TotalAv, OpenAv, CloseAv, ExpQ, NetQ_Qsetup_CurrentQ, QuantityNow As Decimal
        Dim DtSetting, Dtproduct, DtSOH, DtExpired, DtCostcenter As New DataTable
        Dim patchSer As Integer
        Dim Patchname As String
        Dim QCu As Double = 0
        Application.DoEvents()
        Dim Prosstage As Integer = 0
        Dim tot = 100
        Dim F As New Frm_Progress
        F.Show()
        Dim MSG As String = ""
        For Pross = 1 To DGV.Rows.Count - 1

            Prosstage = ((Pross) / (DGV.Rows.Count - 1)) * 100
            F.LblStatus.Text = String.Format("Check Data validation Prossesing..{0}%", Prosstage)
            If Pross > 100 Or Pross < 0 Then F.ProgressBar1.Value = Pross
            F.ProgressBar1.Update()
            F.ProgressBar1.Visible = True
            Thread.Sleep(10)
            DtSetting.Clear()
            DGV.Rows(Pross).Selected = False

            '///////////////////// Load POs Setting
            DtSetting = IFC_Cls.GetSettingPOS(Val(DGV(Pross, "mandant")), Val(DGV(Pross, "outlet")), Val(DGV(Pross, "center")))

            If DtSetting.Rows.Count = 0 Then
                DGV.Rows(Pross).Selected = True
                DGvSCM.DataSource = Nothing
                F.Close()
                'MessageBox.Show("Not Found Company Or Outlet For costCenter " + DGV(Pross, "centername").ToString(), Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Error)
                'F.Close()
                'Return False
                MSG = MSG & "Not Found Company Or Outlet For costCenter " + DGV(Pross, "centername").ToString() & vbNewLine

            End If

            '////////////////Load Product Data
            'If DGV.Rows(Pross)("plu").ToString() = "10106" Then
            '    MessageBox.Show("OK")
            'End If
            Dtproduct.Clear()
            Dtproduct = IFC_Cls.GetProductData(DGV.Rows(Pross)("plu").ToString())
            If Dtproduct.Rows.Count = 0 Then
                DGV.Rows(Pross).Selected = True
                DGvSCM.DataSource = Nothing
                'F.Close()
                'MessageBox.Show(" This Product Code " + DGV(Pross, "plu").ToString() + " Not found In sales Product", Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Error)
                'F.Close()
                'Return False
                MSG = MSG & " This Product Code " + DGV(Pross, "plu").ToString() + " Not found In sales Product" & vbNewLine
            End If
            '///////////////// Load SOH Product ///////////////////////////////////////////
            Dim Quer, loopNo As String
            DtCostcenter.Clear()
            DtCostcenter = IFC_Cls.GetCostCenterData(Val(DtSetting.Rows(0)("Ser")))

            If DtCostcenter.Rows.Count = 0 Then
                DGV.Rows(Pross).Selected = True
                DGvSCM.DataSource = Nothing
                'F.Close()
                'MessageBox.Show("Sorry This Product Not have In This Store " & DtSetting.Rows(0)("Store_NameSCM").ToString(), Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Error)
                'F.Close()
                'Return False
                MSG = MSG & "Sorry This Product Not have In This Store " & DtSetting.Rows(0)("Store_NameSCM").ToString() & vbNewLine
            End If
            loopNo = ""
            For t As Integer = 0 To DtCostcenter.Rows.Count - 1
                If t = 0 Then
                    loopNo = DtCostcenter.Rows(t)("CostCenter_Id").ToString()
                Else
                    loopNo += "," + DtCostcenter.Rows(t)("CostCenter_Id").ToString()
                End If
            Next
            Quer = " and CostCenter_Id in (" + loopNo + ")"

            '//////////////////////////////////////////////////
            DtSOH.Clear()
            DtSOH = IFC_Cls.GetSOHDataGetting(Val(Dtproduct.Rows(0)("Product_Id")), Quer.ToString())  ', Val(DtSOH.Rows(0)("CostCenter_Id"))) ', Val(DtSetting.Rows(0)("CostCenter_IdPOS")))

            If DtSOH.Rows.Count = 0 Then
                DGV.Rows(Pross).Selected = True
                DGvSCM.DataSource = Nothing
                'F.Close()
                'MessageBox.Show("This Product  " + Dtproduct.Rows(0)("Product_Name").ToString() + " Not Linked With CostCenter " + DtSetting.Rows(0)("CostCenter_NamePOS").ToString(), Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Error)
                'F.Close()
                'Return False
                MSG = MSG & "This Product  " + Dtproduct.Rows(0)("Product_Name").ToString() + " Not Linked With CostCenter " + DtSetting.Rows(0)("CostCenter_NamePOS").ToString() & vbNewLine
            End If

            DtCostcenter.Clear()
            DtCostcenter = IFC_Cls.GetCostCenterDataTrue(Val(DtSOH.Rows(0)("CostCenter_Id")))

            If DtCostcenter.Rows.Count = 0 Then
                DGV.Rows(Pross).Selected = True
                DGvSCM.DataSource = Nothing
                'F.Close()
                'MessageBox.Show("Sorry This Product Not have In This Store " & DtSetting.Rows(0)("Store_NameSCM").ToString(), Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Error)
                'F.Close()
                'Return False
                MSG = MSG & "Sorry This Product Not have In This Store " & DtSetting.Rows(0)("Store_NameSCM").ToString() & vbNewLine
            End If
            CostAv = 0
            QuantityNow = 0
            TotalAv = 0
            OpenAv = 0
            CloseAv = 0
            ExpQ = 0
            NetQ_Qsetup_CurrentQ = 0
            '
            Dim ValueV As Boolean
            '/////////////////// Check Period Inventory////////////////////////
            ValueV = IFC_Cls.CheckValiDate(Val(DtCostcenter.Rows(0)("CostCenter_Id")), Convert.ToDateTime(DGV(Pross, "statistdate")), DtCostcenter.Rows(0)("CostCenter_Name"))
            If ValueV = False Then
                DGV.Rows(Pross).Selected = True
                DGvSCM.DataSource = Nothing
                'F.Close()
                'MessageBox.Show("Sorry The Perriod Inventory is Closed With This Costcenter " & DtCostcenter.Rows(0)("CostCenter_Name").ToString(), Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Error)
                'F.Close()
                'Return False
                MSG = MSG & "Sorry The Perriod Inventory is Closed With This Costcenter " & DtCostcenter.Rows(0)("CostCenter_Name").ToString() & vbNewLine
            End If
            'DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Id") = Val(Dtproduct.Rows(0)("Unt_IdSales"))
            'DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Name") = Dtproduct.Rows(0)("Unt_NameSales")
            'DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Q") = Dtproduct.Rows(0)("Unt_QSales")
            'DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = QCu * Dtproduct.Rows(0)("Unt_QSales")
            'DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") / Dtproduct.Rows(0)("Unt_Q") 'QCu '* -1 ' Convert.ToDouble(DGV(Pross, "Qty"))
            'DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") * -1

            CostAv = Dtproduct.Rows(0)("Unt_QSales") / Dtproduct.Rows(0)("Unt_Q")
            CostAv = CostAv * DtSOH.Rows(0)("AvCost")

            NetQ_Qsetup_CurrentQ = Dtproduct.Rows(0)("Unt_QSales") / Dtproduct.Rows(0)("Unt_Q")
            NetQ_Qsetup_CurrentQ = NetQ_Qsetup_CurrentQ * DGV(Pross, "Qty") 'DtSOH.Rows(0)("AvCost")
            'Convert.ToDouble(DGV(Pross, "Qty"))
            TotalAv = NetQ_Qsetup_CurrentQ * CostAv ' DtSOH.Rows(0)("AvCost")
            OpenAv = DtSOH.Rows(0)("Quntity")

            QuantityNow = IFC_Cls.GetQuantityNow(CInt(DtSOH.Rows(0)("Product_Id")), CInt(DtSOH.Rows(0)("CostCenter_Id")), DtSOH.Rows(0)("Product_Code").ToString(), NetQ_Qsetup_CurrentQ)
            CloseAv = DtSOH.Rows(0)("Quntity") - (NetQ_Qsetup_CurrentQ + QuantityNow) ' Convert.ToDouble(DGV(Pross, "Qty"))


            '////////////////////////check soh \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\

            If Convert.ToBoolean(Dtproduct.Rows(0)("IsExpire")) = True Then

                DtSOH.Clear()
                DtSOH = IFC_Cls.GetSOHData(Val(Dtproduct.Rows(0)("Product_Id")), Val(DtCostcenter.Rows(0)("CostCenter_Id"))) ', Val(DtSetting.Rows(0)("CostCenter_IdPOS")))
                'DGV(Pross, "Qty")
                If DtSOH.Rows.Count = 0 Then
                    'returnV = False
                    'MessageBox.Show("Sorry The Product Code " & Dtproduct.Rows(0)("Product_Code") & " Not Link with Costcenter Name " & DtCostcenter.Rows(0)("CostCenter_Name"))
                    'F.Close()
                    'Return returnV
                    MSG = MSG & "Sorry The Product Code " & Dtproduct.Rows(0)("Product_Code") & " Not Link with Costcenter Name " & DtCostcenter.Rows(0)("CostCenter_Name").ToString() & vbNewLine
                Else
                    If NetQ_Qsetup_CurrentQ > (CDec(DtSOH.Rows(0)("Quntity")) + QuantityNow) Then
                        returnV = False
                        'MessageBox.Show("Sorry The Product Code " & Dtproduct.Rows(0)("Product_Code") & " Not Enough Quantity with Costcenter Name " & DtCostcenter.Rows(0)("CostCenter_Name"))
                        'F.Close()
                        'Return returnV
                        MSG = MSG & "Sorry The Product Code " & Dtproduct.Rows(0)("Product_Code") & " Not Enough Quantity with Costcenter Name " & DtCostcenter.Rows(0)("CostCenter_Name").ToString() & vbNewLine
                    End If
                End If

                DtExpired.Clear()
                DtExpired = IFC_Cls.GetExpiredData(Val(Dtproduct.Rows(0)("Product_Id")), Val(DtSOH.Rows(0)("CostCenter_Id")))
                If DtExpired.Rows.Count > 0 Then
                    patchSer = Val(DtExpired.Rows(0)("Patch_Ser"))
                    Patchname = DtExpired.Rows(0)("Patch_Name")

                    If NetQ_Qsetup_CurrentQ > DtExpired.Rows(0)("NetQ_Qsetup_CurrentQ") Then
                        Dim R As Integer = 0
                        Dim QQ As Double = 0

                        Dim Deff As Decimal
                        For Deff = 0 To NetQ_Qsetup_CurrentQ 'Convert.ToDouble(DGV(Pross, "Qty"))
y:
                            DtSOH.Clear()
                            DtSOH = IFC_Cls.GetSOHData(Val(Dtproduct.Rows(0)("Product_Id")), Val(DtCostcenter.Rows(0)("CostCenter_Id"))) ', Val(DtSetting.Rows(0)("CostCenter_IdPOS")))
                            'DGV(Pross, "Qty")
                            If R = 0 Then
                                QQ = NetQ_Qsetup_CurrentQ - DtExpired.Rows(0)("NetQ_Qsetup_CurrentQ")
                                Deff += DtExpired.Rows(0)("NetQ_Qsetup_CurrentQ")
                                QCu = DtExpired.Rows(0)("NetQ_Qsetup_CurrentQ")
                                IFC_Cls.UpdateUsePatch(patchSer, Val(Dtproduct.Rows(0)("Product_Id")), Val(DtSOH.Rows(0)("CostCenter_Id")))
                            Else


                                DtExpired.Clear()
                                DtExpired = IFC_Cls.GetExpiredData(Val(Dtproduct.Rows(0)("Product_Id")), Val(DtSOH.Rows(0)("CostCenter_Id")))
                                If DtExpired.Rows.Count > 0 Then
                                    If QQ > Convert.ToDouble(DtExpired.Rows(0)("NetQ_Qsetup_CurrentQ")) Then
                                        QQ = QQ - DtExpired.Rows(0)("NetQ_Qsetup_CurrentQ")
                                        Deff += DtExpired.Rows(0)("NetQ_Qsetup_CurrentQ")
                                        QCu = DtExpired.Rows(0)("NetQ_Qsetup_CurrentQ")
                                        patchSer = Val(DtExpired.Rows(0)("Patch_Ser"))
                                        Patchname = DtExpired.Rows(0)("Patch_Name")
                                    Else
                                        Deff += QQ
                                        QCu = QQ
                                        patchSer = 0
                                        Patchname = ""

                                    End If
                                    patchSer = Val(DtExpired.Rows(0)("Patch_Ser"))
                                    Patchname = DtExpired.Rows(0)("Patch_Name")
                                    IFC_Cls.UpdateUsePatch(patchSer, Val(Dtproduct.Rows(0)("Product_Id")), Val(DtSOH.Rows(0)("CostCenter_Id")))
                                Else
                                    Deff += QQ
                                    QCu = QQ
                                    patchSer = 0
                                    Patchname = ""
                                End If


                            End If

                            '  CostAv = DtSOH.Rows(0)("AvCost")
                            CostAv = Dtproduct.Rows(0)("Unt_QSales") / Dtproduct.Rows(0)("Unt_Q")
                            CostAv = CostAv * DtSOH.Rows(0)("AvCost")

                            TotalAv = QCu * CostAv ' DtSOH.Rows(0)("AvCost")
                            OpenAv = DtSOH.Rows(0)("Quntity")
                            CloseAv = DtSOH.Rows(0)("Quntity") - QCu 'Convert.ToDouble(DGV(Pross, "Qty"))


                            'CostAv = DtSOH.Rows(0)("AvCost")
                            'TotalAv = Convert.ToDouble(DGV(Pross, "Qty")) * DtSOH.Rows(0)("AvCost")
                            'OpenAv = DtSOH.Rows(0)("Quntity")
                            'CloseAv = Convert.ToDouble(DGV(Pross, "Qty")) - DtSOH.Rows(0)("Quntity")

                            DGvSCM.Rows.Add()
                            DGvSCM(DGvSCM.Rows.Count - 1, "CompanyPOS_Id") = Val(DGV(Pross, "mandant"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "CompanyPOS_Name") = DtSetting.Rows(0)("Company_NamePOS")
                            DGvSCM(DGvSCM.Rows.Count - 1, "OutLetPOS_Id") = Val(DGV(Pross, "outlet"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "OutLetPOS_Name") = DtSetting.Rows(0)("Brand_NamePOS")
                            DGvSCM(DGvSCM.Rows.Count - 1, "CostCenterPOS_Id") = Val(DGV(Pross, "center"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "CostCenterPOS_Name") = DGV(Pross, "centername")
                            DGvSCM(DGvSCM.Rows.Count - 1, "CostCenter_Id_To") = Val(DtCostcenter.Rows(0)("CostCenter_Id"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "CostCenter_Name_To") = DtCostcenter.Rows(0)("CostCenter_Name")
                            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Code") = 0
                            DGvSCM(DGvSCM.Rows.Count - 1, "Product_Id") = Val(Dtproduct.Rows(0)("Product_Id"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "Product_Code") = DGV(Pross, "plu")
                            DGvSCM(DGvSCM.Rows.Count - 1, "Product_Name") = Dtproduct.Rows(0)("Product_Name")
                            DGvSCM(DGvSCM.Rows.Count - 1, "Reciving_Q") = (Dtproduct.Rows(0)("Unt_Q") / Dtproduct.Rows(0)("Unt_QSales")) * (QCu * -1) 'Convert.ToDouble(DGV(Pross, "Qty")) * Val(-1) 'QCu * -1 '
                            DGvSCM(DGvSCM.Rows.Count - 1, "Cost_Product") = CostAv
                            DGvSCM(DGvSCM.Rows.Count - 1, "CostTotalLine") = DGV(Pross, "price") * QCu ' DGV(Pross, "Amount")
                            DGvSCM(DGvSCM.Rows.Count - 1, "Patch_Ser") = patchSer
                            DGvSCM(DGvSCM.Rows.Count - 1, "Patch_Name") = Patchname
                            DGvSCM(DGvSCM.Rows.Count - 1, "Unt_Id") = Val(Dtproduct.Rows(0)("Unt_Id"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "Unt_Name") = Dtproduct.Rows(0)("Unt_Name")
                            DGvSCM(DGvSCM.Rows.Count - 1, "Unt_GroupId") = Dtproduct.Rows(0)("Unt_GroupId")
                            DGvSCM(DGvSCM.Rows.Count - 1, "Unt_Q") = Dtproduct.Rows(0)("Unt_Q")

                            DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Id") = Val(Dtproduct.Rows(0)("Unt_IdSales"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Name") = Dtproduct.Rows(0)("Unt_NameSales")
                            DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Q") = Dtproduct.Rows(0)("Unt_QSales")
                            DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = QCu * -1 '* Dtproduct.Rows(0)("Unt_QSales")
                            'DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") / Dtproduct.Rows(0)("Unt_Q") 'QCu '* -1 ' Convert.ToDouble(DGV(Pross, "Qty"))
                            'DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") * -1

                            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Id") = 0
                            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Date_Create") = Convert.ToDateTime(DGV(Pross, "statistdate"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Submit") = Convert.ToBoolean(True)
                            DGvSCM(DGvSCM.Rows.Count - 1, "TransactionDetails_Ser") = ""
                            DGvSCM(DGvSCM.Rows.Count - 1, "Authulized") = Convert.ToBoolean(True)
                            DGvSCM(DGvSCM.Rows.Count - 1, "SOH") = CloseAv
                            DGvSCM(DGvSCM.Rows.Count - 1, "Open_Q") = OpenAv
                            DGvSCM(DGvSCM.Rows.Count - 1, "Close_Q") = CloseAv
                            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Patch") = ""
                            DGvSCM(DGvSCM.Rows.Count - 1, "Check_No") = DGV(Pross, "billnum")
                            DGvSCM(DGvSCM.Rows.Count - 1, "TotalAvg") = QCu * CostAv * Val(-1)
                            DGvSCM(DGvSCM.Rows.Count - 1, "Is_Recipy") = Convert.ToBoolean(Dtproduct.Rows(0)("IsRecipe"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "Is_Production") = Convert.ToBoolean(Dtproduct.Rows(0)("IsProduction"))

                            DGvSCM(DGvSCM.Rows.Count - 1, "MethodOfPayment_Id") = Val(DGV(Pross, "payform"))

                            DGvSCM(DGvSCM.Rows.Count - 1, "MethodOfPayment_Name") = DGV(Pross, "payformname")
                            DGvSCM(DGvSCM.Rows.Count - 1, "Sales_Price") = DGV(Pross, "price")
                            DGvSCM(DGvSCM.Rows.Count - 1, "IsExpire") = Convert.ToBoolean(Dtproduct.Rows(0)("IsExpire"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "ProductionCode") = 0 ' ""
                            'MessageBox.Show(DGV(Pross, "ChDID"))
                            DGvSCM(DGvSCM.Rows.Count - 1, "ChDID") = Val(DGV(Pross, "ChDID"))
                            IsProductionMy = False
                            If Convert.ToBoolean(Dtproduct.Rows(0)("IsProduction")) = True Then
                                IsProduction = True
                                IsProductionMy = True
                            End If

                            R += 1
                            If Deff < NetQ_Qsetup_CurrentQ Then GoTo y
                        Next

                        GoTo E
                    End If

                Else
                    patchSer = 0
                    Patchname = ""
                End If

            Else
                patchSer = 0
                Patchname = ""
            End If

            If Convert.ToBoolean(Dtproduct.Rows(0)("IsExpire")) = True And patchSer > 0 Then
                IFC_Cls.UpdateUsePatch(patchSer, Val(Dtproduct.Rows(0)("Product_Id")), Val(DtSOH.Rows(0)("CostCenter_Id")))
            End If


            '/////////////////////////////////////////////// Normal No Expired No production ////////////////////////////////////////////
            DGvSCM.Rows.Add()
            DGvSCM(DGvSCM.Rows.Count - 1, "CompanyPOS_Id") = Val(DGV(Pross, "mandant"))
            DGvSCM(DGvSCM.Rows.Count - 1, "CompanyPOS_Name") = DtSetting.Rows(0)("Company_NamePOS")
            DGvSCM(DGvSCM.Rows.Count - 1, "OutLetPOS_Id") = Val(DGV(Pross, "outlet"))
            DGvSCM(DGvSCM.Rows.Count - 1, "OutLetPOS_Name") = DtSetting.Rows(0)("Brand_NamePOS")
            DGvSCM(DGvSCM.Rows.Count - 1, "CostCenterPOS_Id") = Val(DGV(Pross, "center"))
            DGvSCM(DGvSCM.Rows.Count - 1, "CostCenterPOS_Name") = DGV(Pross, "centername")
            DGvSCM(DGvSCM.Rows.Count - 1, "CostCenter_Id_To") = Val(DtCostcenter.Rows(0)("CostCenter_Id"))
            DGvSCM(DGvSCM.Rows.Count - 1, "CostCenter_Name_To") = DtCostcenter.Rows(0)("CostCenter_Name")
            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Code") = 0
            DGvSCM(DGvSCM.Rows.Count - 1, "Product_Id") = Val(Dtproduct.Rows(0)("Product_Id"))
            DGvSCM(DGvSCM.Rows.Count - 1, "Product_Code") = DGV(Pross, "plu")
            DGvSCM(DGvSCM.Rows.Count - 1, "Product_Name") = Dtproduct.Rows(0)("Product_Name")
            DGvSCM(DGvSCM.Rows.Count - 1, "Reciving_Q") = (NetQ_Qsetup_CurrentQ * Dtproduct.Rows(0)("Unt_Q") * Val(-1)) / Dtproduct.Rows(0)("Unt_QSales") 'Convert.ToDouble(DGV(Pross, "Qty"))
            DGvSCM(DGvSCM.Rows.Count - 1, "Cost_Product") = CostAv
            DGvSCM(DGvSCM.Rows.Count - 1, "CostTotalLine") = DGV(Pross, "Amount")
            DGvSCM(DGvSCM.Rows.Count - 1, "Patch_Ser") = patchSer
            DGvSCM(DGvSCM.Rows.Count - 1, "Patch_Name") = Patchname
            DGvSCM(DGvSCM.Rows.Count - 1, "Unt_Id") = Val(Dtproduct.Rows(0)("Unt_Id"))
            DGvSCM(DGvSCM.Rows.Count - 1, "Unt_Name") = Dtproduct.Rows(0)("Unt_Name")
            DGvSCM(DGvSCM.Rows.Count - 1, "Unt_GroupId") = Dtproduct.Rows(0)("Unt_GroupId")
            DGvSCM(DGvSCM.Rows.Count - 1, "Unt_Q") = Dtproduct.Rows(0)("Unt_Q")
            'DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Id") = Val(Dtproduct.Rows(0)("Unt_Id"))
            'DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Name") = Dtproduct.Rows(0)("Unt_Name")
            'DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Q") = Dtproduct.Rows(0)("Unt_GroupId")
            'DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = Convert.ToDouble(DGV(Pross, "Qty"))

            DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Id") = Val(Dtproduct.Rows(0)("Unt_IdSales"))
            DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Name") = Dtproduct.Rows(0)("Unt_NameSales")
            DGvSCM(DGvSCM.Rows.Count - 1, "Current_Unt_Q") = Dtproduct.Rows(0)("Unt_QSales")
            DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = DGV(Pross, "Qty") * Dtproduct.Rows(0)("Unt_QSales")
            DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") / Dtproduct.Rows(0)("Unt_Q") 'QCu '* -1 ' Convert.ToDouble(DGV(Pross, "Qty"))
            DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = DGvSCM(DGvSCM.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") * -1

            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Id") = 0
            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Date_Create") = Convert.ToDateTime(DGV(Pross, "statistdate"))
            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Submit") = Convert.ToBoolean(True)
            DGvSCM(DGvSCM.Rows.Count - 1, "TransactionDetails_Ser") = ""
            DGvSCM(DGvSCM.Rows.Count - 1, "Authulized") = Convert.ToBoolean(True)
            DGvSCM(DGvSCM.Rows.Count - 1, "SOH") = CloseAv
            DGvSCM(DGvSCM.Rows.Count - 1, "Open_Q") = OpenAv
            DGvSCM(DGvSCM.Rows.Count - 1, "Close_Q") = CloseAv
            DGvSCM(DGvSCM.Rows.Count - 1, "Transaction_Patch") = ""
            'DGvSCM(DGvSCM.Rows.Count - 1, "Check_No") = DGV(Pross, "billnum")
            DGvSCM(DGvSCM.Rows.Count - 1, "TotalAvg") = TotalAv * Val(-1)
            DGvSCM(DGvSCM.Rows.Count - 1, "Is_Recipy") = Convert.ToBoolean(Dtproduct.Rows(0)("IsRecipe"))
            DGvSCM(DGvSCM.Rows.Count - 1, "Is_Production") = Convert.ToBoolean(Dtproduct.Rows(0)("IsProduction"))

            'DGvSCM(DGvSCM.Rows.Count - 1, "MethodOfPayment_Id") = Val(DGV(Pross, "payform"))
            'DGvSCM(DGvSCM.Rows.Count - 1, "MethodOfPayment_Name") = DGV(Pross, "payformname")
            DGvSCM(DGvSCM.Rows.Count - 1, "Sales_Price") = DGV(Pross, "price")
            DGvSCM(DGvSCM.Rows.Count - 1, "IsExpire") = Convert.ToBoolean(Dtproduct.Rows(0)("IsExpire"))
            DGvSCM(DGvSCM.Rows.Count - 1, "ProductionCode") = 0 ' ""
            'MessageBox.Show(DGV(Pross, "ChDID"))
            DGvSCM(DGvSCM.Rows.Count - 1, "ChDID") = Val(DGV(Pross, "ChDID"))
            ' DGvSCM(DGvSCM.Rows.Count - 1, "") = ""
            IsProductionMy = False
            If Convert.ToBoolean(Dtproduct.Rows(0)("IsProduction")) = True Then
                IsProduction = True
                IsProductionMy = True
            End If

            returnV = True
            If CloseAv < 0 And IsProductionMy = False Then
                returnV = False
                'MessageBox.Show("Sorry The Quantity Of This Product : " + DGvSCM(DGvSCM.Rows.Count - 1, "Product_Code").ToString() + " / " + DGvSCM(DGvSCM.Rows.Count - 1, "Product_Name").ToString() + " No Quantity Enough In CostCenter : " & DtCostcenter.Rows(0)("CostCenter_Name").ToString())
                'Exit For
                MSG = MSG & "Sorry The Quantity Of This Product : " + DGvSCM(DGvSCM.Rows.Count - 1, "Product_Code").ToString() + " / " + DGvSCM(DGvSCM.Rows.Count - 1, "Product_Name").ToString() + " No Quantity Enough In CostCenter : " & DtCostcenter.Rows(0)("CostCenter_Name").ToString() & vbNewLine
            End If

            If IsProductionMy Then
                Grids_Bulid_prod()
                DGVProduction.Rows(1).Selected = True

                GetProductionRecipyData(DGvSCM.Rows.Count - 1)
                Dim SubM As Boolean = RowEffectQuantity(DGvSCM.Rows.Count - 1, True)
                If SubM = False Then
                    'MessageBox.Show("Sorry The Quantity Of This Product : " + DGvSCM(DGvSCM.Rows.Count - 1, "Product_Code").ToString() + " / " + DGvSCM(DGvSCM.Rows.Count - 1, "Product_Name").ToString() + " No Quantity Enough In CostCenter : " & DtCostcenter.Rows(0)("CostCenter_Name").ToString())
                    'returnV = False
                    'Exit For
                    MSG = MSG & "Sorry The Quantity Of This Product : " + DGvSCM(DGvSCM.Rows.Count - 1, "Product_Code").ToString() + " / " + DGvSCM(DGvSCM.Rows.Count - 1, "Product_Name").ToString() + " No Quantity Enough In CostCenter : " & DtCostcenter.Rows(0)("CostCenter_Name").ToString() & vbNewLine
                End If
            End If

E:
        Next
        ' F.Close()




        F.Close()
        If MSG = "" Then
            returnV = True
        Else
            returnV = False
            Dim FUp As New UploadMSG_Frm(MSG)
            FUp.ShowDialog()
        End If
        Return returnV
    End Function

    Public Sub SaveTransActionsSales()
        On Error Resume Next
        Dim TransCodeSales, TransCodeProduction, Prosstage As Integer
        Dim PatchTransSales, SqllPatch As String
        Dim PatchTransProduction As String = ""
        Dim CollectionPatchSer As String = String.Empty
        TransCodeSales = IFC_Cls.GetTransCodeSales
        IFC_Cls.Head_Insert(TransCodeSales, Val(DGvSCM.Rows(1)("CostCenter_Id_To")), 0, DGvSCM.Rows(1)("CostCenter_Name_To"), "Sales From POS", TransCodeSales, 0, 0, 0, False, True, 1, 5, 0, 0, True, Convert.ToDateTime(DGvSCM.Rows(1)("Transaction_Date_Create")), Convert.ToDateTime(DGvSCM.Rows(1)("Transaction_Date_Create")), "", 1, "", 9)
        PatchTransSales = IFC_Cls.GetPatchHead(Val(TransCodeSales), 9)
        If IsProduction Then
            TransCodeProduction = IFC_Cls.GetTransCodeProduction
            IFC_Cls.Head_Insert(TransCodeProduction, Val(DGvSCM.Rows(1)("CostCenter_Id_To")), 0, DGvSCM.Rows(1)("CostCenter_Name_To"), "Sales From POS", TransCodeSales, 0, 0, 0, False, True, 1, 5, 0, 0, True, Convert.ToDateTime(DGvSCM.Rows(1)("Transaction_Date_Create")), Convert.ToDateTime(DGvSCM.Rows(1)("Transaction_Date_Create")), "", 1, "", 7)
            PatchTransProduction = IFC_Cls.GetPatchHead(Val(TransCodeProduction), 7)
        End If

        Dim Amount_Bill, Amount_BillProduction, TotalAv, OpenAv, CloseAv, Baseunite As Decimal
        Amount_Bill = 0
        Amount_BillProduction = 0
        Dim DtSOH, DtExpired As New DataTable
        'DtRecipy
        Dim F As New Frm_Progress
        F.Show()
        For R As Integer = 1 To DGvSCM.Rows.Count - 1

            '//////////////// deplay Waiting Form
            Prosstage = ((R) / (DGvSCM.Rows.Count - 1)) * 100
            F.LblStatus.Text = String.Format("Load Transaction To SCM Prossesing..{0}%", Prosstage)
            If R > 100 Or R < 0 Then F.ProgressBar1.Value = R
            F.ProgressBar1.Update()
            F.ProgressBar1.Visible = True
            Thread.Sleep(10)
            ''/////////////////// Create Production And Save It In SOH
            'If Convert.ToBoolean(DGvSCM(R, "Is_Production")) = True Then
            '    DtRecipy.Clear()
            '    DGVRecipe.DataSource = DtRecipy
            '    DGVRecipe.DataSource = IFC_Cls.ShowWithProduct_IdAll(Val(DGvSCM(R, "Product_Id")))
            '    Dim TotAvCost As Decimal = 0
            '    '///////////////// fill Recipy DGV////////////////////////////////////////////
            '    DGVRecipe.DataSource = DtRecipy()
            '    '    FillRecipyProduction()
            '    For L As Integer = 1 To DGVRecipe.Rows.Count - 1
            '        DGVRecipe(L, "Recipe_Quantity") = Math.Abs(DGvSCM(R, "NetQ_Qsetup_CurrentQ"))
            '        DGVRecipe(L, "Recipe_Quantity") = Math.Round(Val(DGVRecipe(L, "UsedQuantity")) * Val(DGVRecipe(L, "Recipe_Quantity")), 2)

            '        DGVRecipe(L, "Recipe_Quantity") = Math.Round(Val(DGVRecipe(L, "Recipe_Quantity")) / Val(DGVRecipe(L, "Unt_Q")), 2)

            '        DGVRecipe(L, "Shortage") = Val(DGVRecipe(L, "Stock_On_Hand")) - Val(DGVRecipe(L, "Recipe_Quantity"))
            '        DGVRecipe(L, "NetQ_Qsetup_CurrentQ") = Val(DGVRecipe(L, "Recipe_Quantity"))
            '        DGVRecipe(L, "CostTotalLine") = Math.Round(Val(DGVRecipe(L, "Recipe_Quantity")) * Val(DGVRecipe(L, "Cost_Product")), 2)
            '        TotAvCost += Convert.ToDouble(DGVRecipe(L, "Cost_Product"))


            '        If DGvSCM(R, "IsExpire").ToString() = "True" And Val(DGvSCM(R, "Patch_Ser")) > 0 Then
            '            'Dim Dt As New DataTable
            '            DtExpired.Clear()
            '            DtExpired = IFC_Cls.ShowProductIdExpireDate_(Val(DGvSCM(R, "Product_Id")), Val(DGvSCM(R, "CostCenter_Id_To")))
            '            If DtExpired.Rows.Count = 0 Then
            '                'MessageBox.Show("Sorry This Cost Center Dont Have Expire Patch " & Comp_CostCenter.Text, "Warrning", MessageBoxButtons.OK, MessageBoxIcon.Error)
            '                'DGVProduction.Rows.Remove(DGVProduction.RowSel)
            '                'Btn_Clear_Click(sender, e)
            '                'Return
            '                DGVRecipe(L, "Patch_Ser") = 0 'DtExpired.Rows(0)("Patch_Ser")
            '                DGVRecipe(L, "Patch_Name") = ""
            '            Else
            '                DGVRecipe(L, "Patch_Ser") = DtExpired.Rows(0)("Patch_Ser")
            '                DGVRecipe(L, "Patch_Name") = DtExpired.Rows(0)("Patch_Name")
            '            End If



            '        End If
            '        '///////////////////// save recipy//////////////////////////////////////
            '        Dim PatchN As Integer
            '        Dim AvCost As Decimal
            '        Dim Dt As New DataTable
            '        AvCost = 0
            '        Dt.Clear()

            '        Dt = IFC_Cls.GetSOHData(Val(DGVRecipe(R, "Product_Id")), Val(DGvSCM(R, "CostCenter_Id_To")))

            '        If Dt.Rows.Count > 0 Then
            '            AvCost = Convert.ToDouble(Dt.Rows(0)("AvCost"))
            '        Else
            '            AvCost = 0
            '        End If

            '        If DGVRecipe(R, "Patch_Name").ToString() <> "" Then
            '            SqllPatch = DGVRecipe(R, "Patch_Name").ToString()
            '            PatchN = Val(DGVRecipe(R, "Patch_Ser"))
            '        Else
            '            SqllPatch = ""
            '            PatchN = 0
            '        End If

            '        Dim Totals As Double = 0
            '        Totals = Convert.ToDouble(DGVRecipe(R, "NetQ_Qsetup_CurrentQ")) * AvCost
            '        Totals = Math.Round(Totals, 2)
            '        '    MessageBox.Show(DGVTransfer(R, "IsExpire"))

            '        'For u As Integer = 0 To DGVTransfer.Cols.Count - 1
            '        '    If IsDBNull(DGVTransfer(R, u)) Then
            '        '        MessageBox.Show(DGVTransfer.Cols(u).Name)
            '        '    End If
            '        'Next


            '        'DGVRecipe.Cols("UsedQuantity").Caption = "Unit of Measure"
            '        'DGVRecipe.Cols("Required_Quantity").Caption = "Required Quantity"

            '        IFC_Cls.Details_Save(Val(TransCodeProduction), Val(DGVRecipe(L, "Product_Id")), DGVRecipe(L, "Product_Code"), DGVRecipe(L, "Product_Name"), 0, Val(DGVRecipe(L, "Recipe_Quantity")), Val(DGVRecipe(L, "Recipe_Quantity")), 0, AvCost, Totals, Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"), Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"), 0, "", 0, "", False, False, PatchN, SqllPatch, DGVRecipe(L, "IsExpire"), Val(DGVRecipe(L, "Unt_Id")), DGVRecipe(L, "Unt_Name"), Val(DGVRecipe(L, "Unt_GroupId")), Val(DGVRecipe(L, "Unt_Q")), Val(DGVRecipe(L, "Current_Unt_Id")), DGVRecipe(L, "Current_Unt_Name"), Val(DGVRecipe(L, "Current_Unt_Q")), Convert.ToDouble(DGVRecipe(L, "NetQ_Qsetup_CurrentQ")), Convert.ToDateTime(DGvSCM(R, "Transaction_Date_Create")), False, True, False, False, PatchTransProduction, False, Convert.ToDateTime(DGvSCM(R, "Transaction_Date_Create")), False, AvCost, Convert.ToDouble(DGVRecipe(L, "Required_Quantity")), Convert.ToDouble(DGVRecipe(L, "UsedQuantity")))

            '    Next

            '    '////////////////////////////////// save Production

            '    IFC_Cls.Details_Save(Val(TransCodeProduction), Val(DGvSCM(R, "Product_Id")), DGvSCM(R, "Product_Code"), DGvSCM(R, "Product_Name"), 0, Val(Math.Abs(DGvSCM(R, "Reciving_Q"))), Val(Math.Abs(DGvSCM(R, "Reciving_Q"))), 0, Convert.ToDouble(Math.Abs(DGvSCM(R, "Cost_Product"))), Convert.ToDouble(Math.Abs(DGvSCM(R, "CostTotalLine"))), Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"), Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"), 0, "", 0, "", False, False, Val(DGvSCM(R, "Patch_Ser")), DGvSCM(R, "Patch_Name"), Convert.ToBoolean(DGvSCM(R, "IsExpire")), Val(DGvSCM(R, "Unt_Id")), DGvSCM(R, "Unt_Name"), Val(DGvSCM(R, "Unt_GroupId")), Val(DGvSCM(R, "Unt_Q")), Val(DGvSCM(R, "Current_Unt_Id")), DGvSCM(R, "Current_Unt_Name"), Val(DGvSCM(R, "Current_Unt_Q")), Convert.ToDouble(DGvSCM(R, "NetQ_Qsetup_CurrentQ")), Convert.ToDateTime(DGvSCM(R, "Transaction_Date_Create")), False, True, False, False, PatchTransProduction, True, Convert.ToDateTime(DGvSCM(R, "Transaction_Date_Create")), True, Convert.ToDouble(DGvSCM(R, "OldCost_Product")))
            'End If
            ''///////////////////////////////////////////////Recipy Production //////////////////////////////////////////////

            If Convert.ToBoolean(DGvSCM(R, "Is_Production")) = True Then
                Grids_Bulid_prod()
                DGVProduction.Rows(1).Selected = True

                GetProductionRecipyData(R)
                Dim SubM As Boolean = RowEffectQuantity(R)
                Submit_Btn_Actions(R, PatchTransProduction, TransCodeProduction, 7, Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"))

            End If

            '////////////////////////////////////////////////////////////////////////////////////////////////////////////////
            '///////////////// Get SOH Open Close Q////////////////////////////////
            TotalAv = 0
            OpenAv = 0
            CloseAv = 0
            Baseunite = 0
            DtSOH.Clear()
            DtSOH = IFC_Cls.GetSOHData(Val(DGvSCM(R, "Product_Id")), Val(DGvSCM(R, "CostCenter_Id_To")))
            TotalAv = DGvSCM(R, "NetQ_Qsetup_CurrentQ") * DtSOH.Rows(0)("AvCost")
            OpenAv = DtSOH.Rows(0)("Quntity")
            CloseAv = DtSOH.Rows(0)("Quntity") + DGvSCM(R, "NetQ_Qsetup_CurrentQ")
            Baseunite = DtSOH.Rows(0)("Item_Unit") * DGvSCM(R, "NetQ_Qsetup_CurrentQ")
            Baseunite = Math.Abs(Baseunite)
            'IFC_Cls.UpdateSOH(Val(DGvSCM(R, "Product_Id")), Val(DGvSCM(R, "CostCenter_Id_To")), CloseAv, Baseunite)

            '//////////////////// Update Patch ///////////////////////////////////////////////////////////////////////

            If Convert.ToBoolean(DGvSCM(R, "IsExpire")) = True And Val(DGvSCM(R, "Patch_Ser")) > 0 Then
                '    IFC_Cls.UpdatePatchExpired(Val(DGvSCM(R, "Patch_Ser")), Convert.ToDecimal(DGvSCM(R, "NetQ_Qsetup_CurrentQ")))
                CollectionPatchSer += DGvSCM(R, "Patch_Ser").ToString()
            End If

            '//////////////////////////////////////////////////////////////////////////////////////////////////////////

            'DGvSCM(R, "CompanyPOS_Id") = Val(DGV(Pross, "mandant"))
            'DGvSCM(R, "CompanyPOS_Name") = DtSetting.Rows(0)("Company_NamePOS")
            'DGvSCM(R, "OutLetPOS_Id") = Val(DGV(Pross, "outlet"))
            'DGvSCM(R, "OutLetPOS_Name") = DtSetting.Rows(0)("Brand_NamePOS")
            'DGvSCM(R, "CostCenterPOS_Id") = Val(DGV(Pross, "center"))
            'DGvSCM(R, "CostCenterPOS_Name") = DGV(Pross, "centername")
            'DGvSCM(R, "CostCenter_Id_To") = Val(DtCostcenter.Rows(0)("CostCenter_Id"))
            'DGvSCM(R, "CostCenter_Name_To") = DtCostcenter.Rows(0)("CostCenter_Name")
            'DGvSCM(R, "Transaction_Code") = 0
            'DGvSCM(R, "Product_Id") = Val(Dtproduct.Rows(0)("Product_Id"))
            'DGvSCM(R, "Product_Code") = DGV(Pross, "plu")
            'DGvSCM(R, "Product_Name") = Dtproduct.Rows(0)("Product_Name")
            'DGvSCM(R, "Reciving_Q") = (Dtproduct.Rows(0)("Unt_Q") / Dtproduct.Rows(0)("Unt_QSales")) * (QCu * -1) 'Convert.ToDouble(DGV(Pross, "Qty")) * Val(-1) 'QCu * -1 '
            'DGvSCM(R, "Cost_Product") = CostAv
            'DGvSCM(R, "CostTotalLine") = DGV(Pross, "price") * QCu ' DGV(Pross, "Amount")
            'DGvSCM(R, "Patch_Ser") = patchSer
            'DGvSCM(R, "Patch_Name") = Patchname
            'DGvSCM(R, "Unt_Id") = Val(Dtproduct.Rows(0)("Unt_Id"))
            'DGvSCM(R, "Unt_Name") = Dtproduct.Rows(0)("Unt_Name")
            'DGvSCM(R, "Unt_GroupId") = Dtproduct.Rows(0)("Unt_GroupId")
            'DGvSCM(R, "Unt_Q") = Dtproduct.Rows(0)("Unt_Q")

            'DGvSCM(R, "Current_Unt_Id") = Val(Dtproduct.Rows(0)("Unt_IdSales"))
            'DGvSCM(R, "Current_Unt_Name") = Dtproduct.Rows(0)("Unt_NameSales")
            'DGvSCM(R, "Current_Unt_Q") = Dtproduct.Rows(0)("Unt_QSales")
            'DGvSCM(R, "NetQ_Qsetup_CurrentQ") = QCu * -1 '* Dtproduct.Rows(0)("Unt_QSales")
            ''DGvSCM(r, "NetQ_Qsetup_CurrentQ") = DGvSCM(r, "NetQ_Qsetup_CurrentQ") / Dtproduct.Rows(0)("Unt_Q") 'QCu '* -1 ' Convert.ToDouble(DGV(Pross, "Qty"))
            ''DGvSCM(r, "NetQ_Qsetup_CurrentQ") = DGvSCM(r, "NetQ_Qsetup_CurrentQ") * -1

            'DGvSCM(R, "Transaction_Id") = 0
            'DGvSCM(R, "Transaction_Date_Create") = Convert.ToDateTime(DGV(Pross, "statistdate"))
            'DGvSCM(R, "Transaction_Submit") = Convert.ToBoolean(True)
            'DGvSCM(R, "TransactionDetails_Ser") = ""
            'DGvSCM(R, "Authulized") = Convert.ToBoolean(True)
            'DGvSCM(R, "SOH") = CloseAv
            'DGvSCM(R, "Open_Q") = OpenAv
            'DGvSCM(R, "Close_Q") = CloseAv
            'DGvSCM(R, "Transaction_Patch") = ""
            'DGvSCM(R, "Check_No") = DGV(Pross, "billnum")
            'DGvSCM(R, "TotalAvg") = QCu * CostAv * Val(-1)
            'DGvSCM(R, "Is_Recipy") = Convert.ToBoolean(Dtproduct.Rows(0)("IsRecipe"))
            'DGvSCM(R, "Is_Production") = Convert.ToBoolean(Dtproduct.Rows(0)("IsProduction"))

            'DGvSCM(R, "MethodOfPayment_Id") = Val(DGV(Pross, "payform"))
            'DGvSCM(R, "MethodOfPayment_Name") = DGV(Pross, "payformname")
            'DGvSCM(R, "Sales_Price") = DGV(Pross, "price")
            'DGvSCM(R, "IsExpire") = Convert.ToBoolean(Dtproduct.Rows(0)("IsExpire"))
            'DGvSCM(R, "ProductionCode") = 0 ' ""
            Dim Serr As Integer = 0
            '/////////////////////////////////////SALES TRANSACTIONS////////////////////////////////////////////////////////////
            'Dim msg As String = ""
            'msg = Val(TransCodeSales) & "," & Val(DGvSCM(R, "Product_Id")) & "," & DGvSCM(R, "Product_Code") & "," & DGvSCM(R, "Product_Name") & "," & Val(0) & "," & Val(DGvSCM(R, "Reciving_Q")) & "," & Val(DGvSCM(R, "Reciving_Q")) & "," & Val(0) & "," & Convert.ToDouble(DGvSCM(R, "Cost_Product")) & "," & Convert.ToDouble(DGvSCM(R, "CostTotalLine")) & "," & Val(DGvSCM(R, "CostCenter_Id_To")) & "," & DGvSCM(R, "CostCenter_Name_To") & "," & Val(DGvSCM(R, "CostCenter_Id_To")) & "," & DGvSCM(R, "CostCenter_Name_To") & "," & 0 & "," & "" & "," & 0 & "," & "" & "," & False & "," & False & "," & Val(DGvSCM(R, "Patch_Ser")) & "," & DGvSCM(R, "Patch_Name") & "," & DGvSCM(R, "IsExpire") & "," & Val(DGvSCM(R, "Unt_Id")) & "," & DGvSCM(R, "Unt_Name") & "," & Val(DGvSCM(R, "Unt_GroupId")) & "," & Val(DGvSCM(R, "Unt_Q")) & "," & Val(DGvSCM(R, "Current_Unt_Id")) & "," & DGvSCM(R, "Current_Unt_Name") & "," & Val(DGvSCM(R, "Current_Unt_Q")) & "," & Convert.ToDouble(DGvSCM(R, "NetQ_Qsetup_CurrentQ")) & "," & DGvSCM(R, "Transaction_Date_Create") & "," & 1 & "," & 1 & "," & False & "," & False & "," & PatchTransSales & "," & True & "," & Convert.ToDateTime(DGvSCM(R, "Transaction_Date_Create"))
            'MessageBox.Show(msg)
            Serr = IFC_Cls.Details_SaveSales(Val(TransCodeSales), Val(DGvSCM(R, "Product_Id")), DGvSCM(R, "Product_Code"), DGvSCM(R, "Product_Name"), Val(0), Val(DGvSCM(R, "Reciving_Q")), Val(DGvSCM(R, "Reciving_Q")), Val(0), Convert.ToDouble(DGvSCM(R, "Cost_Product")), Convert.ToDouble(DGvSCM(R, "CostTotalLine")), Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"), Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"), 0, "", 0, "", False, False, Val(DGvSCM(R, "Patch_Ser")), DGvSCM(R, "Patch_Name"), DGvSCM(R, "IsExpire"), Val(DGvSCM(R, "Unt_Id")), DGvSCM(R, "Unt_Name"), Val(DGvSCM(R, "Unt_GroupId")), Val(DGvSCM(R, "Unt_Q")), Val(DGvSCM(R, "Current_Unt_Id")), DGvSCM(R, "Current_Unt_Name"), Val(DGvSCM(R, "Current_Unt_Q")), Convert.ToDouble(DGvSCM(R, "NetQ_Qsetup_CurrentQ")), DGvSCM(R, "Transaction_Date_Create"), 1, 1, False, False, PatchTransSales, True, Convert.ToDateTime(DGvSCM(R, "Transaction_Date_Create")))



            '//////////////////////////////////////////Save In Sales POS////////
            'msg = ""
            'msg = Val(DGvSCM(R, "CostCenterPOS_Id"))
            'msg = msg & "," & DGvSCM(R, "CostCenterPOS_Name")
            'msg = msg & "," & Val(DGvSCM(R, "CostCenter_Id_To")) & ","
            'msg = msg & DGvSCM(R, "CostCenter_Name_To") & ","
            'msg = msg & DGvSCM(R, "Transaction_Code") & ","
            'msg = msg & Val(DGvSCM(R, "Product_Id")) & ","
            'msg = msg & DGvSCM(R, "Product_Code") & ","
            'msg = msg & DGvSCM(R, "Product_Name") & ","
            'msg = msg & DGvSCM(R, "Reciving_Q") & ","
            'msg = msg & DGvSCM(R, "Cost_Product") & ","
            'msg = msg & DGvSCM(R, "CostTotalLine") & ","
            'msg = msg & Val(DGvSCM(R, "Patch_Ser")) & ","
            'msg = msg & DGvSCM(R, "Patch_Name") & ","
            'msg = msg & Val(DGvSCM(R, "Unt_Id")) & ","
            'msg = msg & DGvSCM(R, "Unt_Name") & ","
            'msg = msg & Val(DGvSCM(R, "Unt_GroupId")) & ","
            'msg = msg & DGvSCM(R, "Unt_Q") & ","
            'msg = msg & Val(DGvSCM(R, "Current_Unt_Id")) & ","
            'msg = msg & DGvSCM(R, "Current_Unt_Name") & ","
            'msg = msg & DGvSCM(R, "Current_Unt_Q") & ","
            'msg = msg & DGvSCM(R, "NetQ_Qsetup_CurrentQ") & ","
            ''   msg = msg & Val(DGvSCM(R, "Transaction_Id")) & ","
            'msg = msg & DGvSCM(R, "Transaction_Date_Create") & ","
            'msg = msg & DGvSCM(R, "Transaction_Submit") & ","
            'msg = msg & Val(Serr) & ","
            'msg = msg & DGvSCM(R, "Authulized") & ","
            'msg = msg & DGvSCM(R, "SOH") & ","
            'msg = msg & DGvSCM(R, "Open_Q") & ","
            'msg = msg & DGvSCM(R, "Close_Q") & ","
            ''  msg = msg & DGvSCM(R, "Transaction_Patch") & ","
            'msg = msg & Val(DGvSCM(R, "Check_No")) & ","
            'msg = msg & DGvSCM(R, "TotalAvg") & ","
            'msg = msg & DGvSCM(R, "Is_Recipy") & ","
            'msg = msg & DGvSCM(R, "Is_Production") & ","
            'msg = msg & Val(DGvSCM(R, "CompanyPOS_Id")) & ","
            'msg = msg & DGvSCM(R, "CompanyPOS_Name") & ","
            'msg = msg & Val(DGvSCM(R, "OutLetPOS_Id")) & ","
            'msg = msg & DGvSCM(R, "OutLetPOS_Name") & ","
            'msg = msg & Val(DGvSCM(R, "MethodOfPayment_Id")) & ","
            'msg = msg & DGvSCM(R, "MethodOfPayment_Name") & ","
            'msg = msg & DGvSCM(R, "Sales_Price") & ","
            'msg = msg & DGvSCM(R, "IsExpire") & ","
            'msg = msg & DGvSCM(R, "ProductionCode")

            'MessageBox.Show(msg)

            'IFC_Cls.SaveSalesPos(Val(DGvSCM(R, "CostCenterPOS_Id")), DGvSCM(R, "CostCenterPOS_Name"), Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"), TransCodeSales, Val(DGvSCM(R, "Product_Id")), DGvSCM(R, "Product_Code"), DGvSCM(R, "Product_Name"), DGvSCM(R, "Reciving_Q"), DGvSCM(R, "Cost_Product"), DGvSCM(R, "CostTotalLine"), Val(DGvSCM(R, "Patch_Ser")), DGvSCM(R, "Patch_Name"), Val(DGvSCM(R, "Unt_Id")), DGvSCM(R, "Unt_Name"), Val(DGvSCM(R, "Unt_GroupId")), DGvSCM(R, "Unt_Q"), Val(DGvSCM(R, "Current_Unt_Id")), DGvSCM(R, "Current_Unt_Name"), DGvSCM(R, "Current_Unt_Q"), DGvSCM(R, "NetQ_Qsetup_CurrentQ"), Val(9), DGvSCM(R, "Transaction_Date_Create"), DGvSCM(R, "Transaction_Submit"), Val(Serr), DGvSCM(R, "Authulized"), DGvSCM(R, "SOH"), DGvSCM(R, "Open_Q"), DGvSCM(R, "Close_Q"), PatchTransSales, Val(DGvSCM(R, "Check_No")), DGvSCM(R, "TotalAvg"), DGvSCM(R, "Is_Recipy"), DGvSCM(R, "Is_Production"), Val(DGvSCM(R, "CompanyPOS_Id")), DGvSCM(R, "CompanyPOS_Name"), Val(DGvSCM(R, "OutLetPOS_Id")), DGvSCM(R, "OutLetPOS_Name"), Val(DGvSCM(R, "MethodOfPayment_Id")), DGvSCM(R, "MethodOfPayment_Name"), DGvSCM(R, "Sales_Price"), DGvSCM(R, "IsExpire"), DGvSCM(R, "ProductionCode"))

            IFC_Cls.SaveSalesPos(Val(DGvSCM(R, "CostCenterPOS_Id")), DGvSCM(R, "CostCenterPOS_Name"), Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"), TransCodeSales, Val(DGvSCM(R, "Product_Id")), DGvSCM(R, "Product_Code"), DGvSCM(R, "Product_Name"), DGvSCM(R, "Reciving_Q"), DGvSCM(R, "Cost_Product"), DGvSCM(R, "CostTotalLine"), Val(DGvSCM(R, "Patch_Ser")), DGvSCM(R, "Patch_Name"), Val(DGvSCM(R, "Unt_Id")), DGvSCM(R, "Unt_Name"), Val(DGvSCM(R, "Unt_GroupId")), DGvSCM(R, "Unt_Q"), Val(DGvSCM(R, "Current_Unt_Id")), DGvSCM(R, "Current_Unt_Name"), DGvSCM(R, "Current_Unt_Q"), DGvSCM(R, "NetQ_Qsetup_CurrentQ"), Val(9), DGvSCM(R, "Transaction_Date_Create"), DGvSCM(R, "Transaction_Submit"), Val(Serr), DGvSCM(R, "Authulized"), DGvSCM(R, "SOH"), DGvSCM(R, "Open_Q"), DGvSCM(R, "Close_Q"), PatchTransSales, Val(0), DGvSCM(R, "TotalAvg"), DGvSCM(R, "Is_Recipy"), DGvSCM(R, "Is_Production"), Val(DGvSCM(R, "CompanyPOS_Id")), DGvSCM(R, "CompanyPOS_Name"), Val(DGvSCM(R, "OutLetPOS_Id")), DGvSCM(R, "OutLetPOS_Name"), Val(0), "POS Sales", DGvSCM(R, "Sales_Price"), DGvSCM(R, "IsExpire"), DGvSCM(R, "ProductionCode"))
        Next
        '////////////////////// Update USE Patch False

        If CollectionPatchSer <> "" Then IFC_Cls.UpdatePatcheUseFalse(CollectionPatchSer)
        '//////////////////////  Update TransHead Amount

        IFC_Cls.UpdateHeadAmount(Val(TransCodeSales), 9, Amount_Bill)

        '/////////////////////Update Production Amount
        If IsProduction Then
            IFC_Cls.UpdateHeadAmount(Val(TransCodeProduction), 7, Amount_BillProduction)
        End If
        F.Close()

    End Sub
    Public Sub Submit_Btn_Actions(RO As Integer, PatchTrans As String, TransCode As String, TransId As Integer, CostCenterID As Integer, CostCenterName As String)
        Dim Dt As New DataTable
        Dim DTP As New DateTimePicker
        DTP.Value = Date.Now
        'Validatingg = ClsValid.Empty_CompValidations(Comp_CostCenter, "FillData")
        'If Validatingg = True Then

        '    Return
        'End If

        'Validatingg = CheckTrueDateFuture(DTP.Value)
        'If Validatingg = True Then
        '    DTP.Focus()

        '    Return
        'End If

        'Validatingg = ClsValid.Empty_TxtValidations(TxtRemarks, "FillData")
        'If Validatingg = True Then

        '    Return
        'End If



        '/////////////////////// Validation Details///////////////////////////////////////////////////

        'For U As Integer = 1 To DGVProduction.Rows.Count - 2
        '    '  MessageBox.Show(DGVTransfer(R, "NetQ_Qsetup_CurrentQ"))
        '    DGVProduction.Rows(U).Selected = False
        '    If DGVProduction(U, "Product_Id").ToString() = "" Or DGVProduction(U, "Current_Unt_Name").ToString() = "" Or Val(DGVProduction(U, "Reciving_Q")) = 0 Or Val(DGVProduction(U, "CostTotalLine")) = 0 Or Val(DGVProduction(U, "NetQ_Qsetup_CurrentQ")) = 0 Then
        '        Dt.Clear()

        '        MessageBox.Show(DGVProduction(U, "Product_Id").ToString() & "-" & DGVProduction(U, "Current_Unt_Name").ToString() & "-" & Val(DGVProduction(U, "Reciving_Q")) & "-" & Val(DGVProduction(U, "CostTotalLine")) & "-" & Val(DGVProduction(U, "NetQ_Qsetup_CurrentQ")))
        '        Dt = Clsmsg.SelectMsg("FillData")
        '        MessageBox.Show(Dt.Rows(0)(1).ToString(), Dt.Rows(0)(2).ToString(), MessageBoxButtons.OK, MessageBoxIcon.Error)

        '        DGVProduction.Rows(U).Selected = True
        '        TabContainer.SelectedTab = TabRecipe

        '        Return

        '    End If
        'Next

        'For R As Integer = 1 To DGVRecipe.Rows.Count - 1
        '    If DGVRecipe(R, "Patch_Name").ToString() = "" And DGVRecipe(R, "IsExpire").ToString() = "True" Then

        '        Dt.Clear()
        '        Dt = Clsmsg.SelectMsg("CreatePatch")
        '        MessageBox.Show(Dt.Rows(0)(1).ToString(), Dt.Rows(0)(2).ToString(), MessageBoxButtons.OK, MessageBoxIcon.Error)

        '        DGVRecipe.Rows(R).Selected = True
        '        '   TabContainer.SelectedTab = TabRecipe

        '        Return
        '    End If

        'Next

        '////////////////////////////// Validation Period/////////////////////////////
        Dim NetQ As Double = 0
        '  If Submting = True And Autholizing = True Then


        Dim ValueV As Boolean
        For R As Integer = 1 To DGVRecipe.Rows.Count - 1
            NetQ = 0
            ValueV = False
            DGVRecipe.Rows(R).Selected = False
            ValueV = IFC_Cls.CheckValiDate(CostCenterID, Convert.ToDateTime(DGvSCM(RO, "Transaction_Date_Create")), CostCenterName)
            If ValueV = False Then


                DGVRecipe.Rows(R).Selected = True
                Return
            End If

            '//////////////////// Check Stoc /////////////////////////////////////
            Dt.Clear()
            Dt = ClsStock.Show_StockWithParam(Val(DGVRecipe(R, "Product_Id")), CostCenterID)

            If Dt.Rows.Count = 0 Then

                NetQ = 0
            Else
                NetQ = Convert.ToDouble(Dt.Rows(0)("Quntity")) * Convert.ToDouble(Dt.Rows(0)("Item_Unit"))
                'DGVRecipe.Rows(R).Selected = True
                'MessageBox.Show("Sorry This Quantity Is Big More Than Quantity STock In Cost Center: " & CostCenterName, "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                'Return

            End If



            'If Convert.ToDouble(DGVRecipe(R, "NetQ_Qsetup_CurrentQ")) > NetQ Then
            '    'Submting = False
            '    'Saving = True

            '    DGVRecipe.Rows(R).Selected = True
            '    MessageBox.Show("Sorry This Quantity Is Big More Than Quantity STock In Cost Center: " & CostCenterName, "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            '    Return

            'End If
            '////////////////////////Validation Patch Q
            If DGVRecipe(R, "IsExpire").ToString() = "True" Then

                'ClsPatch
                Dt.Clear()
                Dt = IFC_Cls.CheckSumQPatch(Val(DGVRecipe(R, "Product_Id")), CostCenterID)
                If Dt.Rows.Count = 0 Then

                    NetQ = 0
                Else
                    NetQ = 0
                    NetQ = Convert.ToDouble(Dt.Rows(0)("PatchQ"))

                    'DGVRecipe.Rows(R).Selected = True
                    'MessageBox.Show("Sorry This Quantity Is Big More Than Quantity In Expire Patch", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    'Return

                End If

                'NetQ = 0
                'NetQ = Convert.ToDouble(Dt.Rows(0)("PatchQ"))
                '    If Convert.ToDouble(DGVRecipe(R, "NetQ_Qsetup_CurrentQ")) > NetQ Then


                '        DGVRecipe.Rows(R).Selected = True
                '        MessageBox.Show("Sorry This Quantity Is Big More Than Quantity In Expire Patch", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                '        Return

                '    End If
            End If
        Next
        'End If


        '///////////////////////////// Add New Patch Row////////////////////////////////////////////////////////
        'If Submting = True And Autholizing = True Then
        Dim QQQ, Def As Double
YOYO:
        For P As Integer = 0 To DGVRecipe.Rows.Count - 1
            If DGVRecipe(P, "IsExpire").ToString() <> "True" Then GoTo ff
            QQQ = 0
            Dt.Clear()
            Dt = IFC_Cls.ShowSerOpen_(Val(DGVRecipe(P, "Patch_Ser")), CostCenterID)
            QQQ = Val(Dt.Rows(0)("NetQ_Qsetup_CurrentQ"))
            If Convert.ToDouble(DGVRecipe(P, "NetQ_Qsetup_CurrentQ")) > Val(QQQ) Then

                Def = Val(DGVRecipe(P, "NetQ_Qsetup_CurrentQ")) - Val(QQQ)
                DGVRecipe(P, "CostTotalLine") = Math.Round(Val(QQQ * DGVRecipe(P, "Cost_Product")), 2) ' Math.Round(Val(DGVRecipe(P, "NetQ_Qsetup_CurrentQ") * DGVRecipe(P, "Cost_Product")), 2)
                DGVRecipe(P, "NetQ_Qsetup_CurrentQ") = Val(QQQ)
                QQQ = Val(QQQ) / DGVRecipe(P, "Current_Unt_Q")
                QQQ = Val(QQQ) * DGVRecipe(P, "Unt_Q")
                DGVRecipe(P, "Recipe_Quantity") = QQQ

                'User=1 For This Patch

                IFC_Cls.UpdatePatchUse_True(Val(DGVRecipe(P, "Patch_Ser")))
                '//////////////////////////////New Row//////////////////////////
                DGVRecipe.Rows.Add()
                For Co As Integer = 0 To DGVRecipe.Cols.Count - 1
                    DGVRecipe(DGVRecipe.Rows.Count - 1, Co) = DGVRecipe(P, Co)
                Next
                '    Def = Val(DGVTransfer(P, "NetQ_Qsetup_CurrentQ")) - Val(QQQ)
                'Get Next Patch
                Dt.Clear()
                Dt = IFC_Cls.ShowUsdFalse_(DGVRecipe(P, "Product_Id"), CostCenterID)
                If Dt.Rows.Count > 0 Then
                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Patch_Name") = Dt.Rows(0)("Patch_Name")
                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Patch_Ser") = Dt.Rows(0)("Patch_Ser")
                    DGVRecipe(DGVRecipe.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = Val(Def)
                    DGVRecipe(DGVRecipe.Rows.Count - 1, "CostTotalLine") = Math.Round(Val(DGVRecipe(DGVRecipe.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") * DGVRecipe(DGVRecipe.Rows.Count - 1, "Cost_Product")), 2)
                    Def = Val(Def) / DGVRecipe(DGVRecipe.Rows.Count - 1, "Current_Unt_Q")
                    Def = Val(Def) * DGVRecipe(DGVRecipe.Rows.Count - 1, "Unt_Q")

                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Recipe_Quantity") = Def '* DGVRecipe(DGVRecipe.Rows.Count - 1, "Current_Unt_Q")

                End If
                ' GetTotalCost()
                Application.DoEvents()
                GoTo YOYO
            End If
ff:
        Next


        ' End If

        '////////////////////////////////////////Save Head/////////////////////////////////////////////////////
        Dim SqllPatch As String = ""
        Dim PatchN As Integer = 0


        Dim userIdSubmit As Integer = 0
        Dim AuthId As Integer = Val(5)


        userIdSubmit = Val(1)





        'If Submting = True And Autholizing = False Then
        '    ' userIdSubmit = Val(Frm_Main.TxtUserId.Text)
        '    If AuthId < 5 Then
        '        AuthId += 1
        '    End If

        'End If
        'Or TxtTransaction_Patch.Text = ""
        '  If Val(TxtTransaction_CodeTransfer.Text) <= 0 Then



        Dt.Clear()
        'Dt = ClsProduction.GetTransCode()
        'Dim Transaction_Patch As String = ""
        'Dim Transaction_Code As Integer = 0
        'Transaction_Code = Val(Dt.Rows(0)(0))
        Dim Totaltxt As Double
        For TT As Integer = 1 To DGVProduction.Rows.Count - 2
            Totaltxt += Convert.ToDouble(DGVProduction(TT, "CostTotalLine"))
        Next
        '  ClsOrderReq.Head_Insert(Val(Transaction_Code), Comp_CostCenter.SelectedValue, 0, Comp_CostCenter.Text, TxtRemarks.Text, Val(Dt.Rows(0)(0)), Convert.ToDouble(TxtTotal.Text), Convert.ToDouble(TxtTax.Text), Convert.ToDouble(TxtNetTotal.Text), Saving, Submting, Val(Frm_Main.TxtUserId.Text), Val(AuthId), 0, Convert.ToDouble(TxtNetTotal.Text), Autholizing, DTP.Value.ToString("yyyy-MM-dd HH:mm:ss tt"), Date.Now.ToString("yyyy-MM-dd HH:mm:ss tt"), TxtTransaction_Patch.Text, userIdSubmit)
        '   ClsProduction.Head_Insert(Val(TransCode), Comp_CostCenter.SelectedValue, 0, Comp_CostCenter.Text, TxtRemarks.Text, Val(Dt.Rows(0)(0)), Totaltxt, 0, Totaltxt, False, True, Val(Frm_Main.TxtUserId.Text), Val(AuthId), 0, Totaltxt, True, DTP.Value, Date.Now, "", userIdSubmit)


        'Dt.Clear()
        'Dt = ClsProduction.ShowHeadWithConditions_(" and Transaction_Code='" & Transaction_Code & "'")
        'If Dt.Rows.Count > 0 Then
        '    Transaction_Patch = Dt.Rows(0)("Transaction_Patch").ToString()
        'End If
        'Dim R As Integer
        For R As Integer = 1 To DGVProduction.Rows.Count - 2
            '  MessageBox.Show(DGVTransfer(R, "Patch_Name").ToString())



            If DGVProduction(R, "Patch_Name").ToString() <> "" Then
                SqllPatch = DGVProduction(R, "Patch_Name").ToString()
                PatchN = Val(DGVProduction(R, "Patch_Ser"))
            Else
                SqllPatch = ""
                PatchN = 0
            End If
            '    MessageBox.Show(DGVTransfer(R, "IsExpire"))

            'For u As Integer = 0 To DGVTransfer.Cols.Count - 1
            '    If IsDBNull(DGVTransfer(R, u)) Then
            '        MessageBox.Show(DGVTransfer.Cols(u).Name)
            '    End If
            'Next
            Dim NewAv, TotNewQ, TotAvCost As Double
            Dt.Clear()

            '   Dt = ClsProduct.ShowProduct_(Val(DGVProduction(R, "Product_Id")))
            Dt = ClsStock.Show_StockWithParam(Val(DGVRecipe(R, "Product_Id")), CostCenterID)
            If Dt.Rows.Count > 0 Then
                TotAvCost = Convert.ToDouble(Dt.Rows(0)("AvCost"))
            Else
                TotAvCost = 0
            End If
            'TotAvCost = Val(TotAvCost) * Val(DGVProduction(R, "Reciving_Q"))

            'DGVProduction(R, "CostTotalLine") = Math.Round(TotAvCost, 2)
            'NewAv = Val(DGVProduction(R, "InStock")) * Val(DGVProduction(R, "OldCost_Product"))
            'TotNewQ = Val(DGVProduction(R, "InStock")) + Val(DGVProduction(R, "Reciving_Q"))

            'TotAvCost += NewAv
            'TotAvCost = Math.Round(TotAvCost / TotNewQ, 2)
            ''     DGVProduction(DGVProduction.RowSel, "NetQ_Qsetup_CurrentQ") = Val(DGVProduction(DGVProduction.RowSel, "Reciving_Q"))
            'TotAvCost = Val(TotAvCost) * Val(DGVProduction(R, "InStock"))
            'NewAv=

            'DGVProduction(R, "OldCost_Product") = TotAvCost

            'MessageBox.Show(DGVProduction(R, "OldCost_Product"))
            IFC_Cls.Details_Save(Val(TransCode), Val(DGVProduction(R, "Product_Id")), DGVProduction(R, "Product_Code"), DGVProduction(R, "Product_Name"), 0, Val(DGVProduction(R, "Reciving_Q")), Val(DGVProduction(R, "Reciving_Q")), 0, Convert.ToDouble(DGVProduction(R, "Cost_Product")), Convert.ToDouble(DGVProduction(R, "CostTotalLine")), CostCenterID, CostCenterName, CostCenterID, CostCenterName, 0, "", 0, "", False, False, PatchN, SqllPatch, DGVProduction(R, "IsExpire"), Val(DGVProduction(R, "Unt_Id")), DGVProduction(R, "Unt_Name"), Val(DGVProduction(R, "Unt_GroupId")), Val(DGVProduction(R, "Unt_Q")), Val(DGVProduction(R, "Current_Unt_Id")), DGVProduction(R, "Current_Unt_Name"), Val(DGVProduction(R, "Current_Unt_Q")), Convert.ToDouble(DGVProduction(R, "NetQ_Qsetup_CurrentQ")), DTP.Value, False, True, False, False, PatchTrans, True, DGvSCM(RO, "Transaction_Date_Create"), True, Convert.ToDouble(DGVProduction(R, "OldCost_Product")))
        Next

        Dim AvCost As Double
        For R As Integer = 1 To DGVRecipe.Rows.Count - 1
            '  MessageBox.Show(DGVRecipe(R, "Product_Name").ToString())
            AvCost = 0
            Dt.Clear()

            '  Dt = ClsProduct.ShowProduct_(Val(DGVRecipe(R, "Product_Id")))
            Dt = ClsStock.Show_StockWithParam(Val(DGVRecipe(R, "Product_Id")), CostCenterID)
            If Dt.Rows.Count > 0 Then
                AvCost = Convert.ToDouble(Dt.Rows(0)("AvCost"))
            Else
                AvCost = 0
            End If

            If DGVRecipe(R, "Patch_Name").ToString() <> "" Then
                SqllPatch = DGVRecipe(R, "Patch_Name").ToString()
                PatchN = Val(DGVRecipe(R, "Patch_Ser"))
            Else
                SqllPatch = ""
                PatchN = 0
            End If

            Dim Totals As Double = 0
            Totals = Convert.ToDouble(DGVRecipe(R, "NetQ_Qsetup_CurrentQ")) * AvCost
            Totals = Math.Round(Totals, 2)
            '    MessageBox.Show(DGVTransfer(R, "IsExpire"))

            'For u As Integer = 0 To DGVTransfer.Cols.Count - 1
            '    If IsDBNull(DGVTransfer(R, u)) Then
            '        MessageBox.Show(DGVTransfer.Cols(u).Name)
            '    End If
            'Next


            'DGVRecipe.Cols("UsedQuantity").Caption = "Unit of Measure"
            'DGVRecipe.Cols("Required_Quantity").Caption = "Required Quantity"

            'Dim SS As String
            'SS = Val(TransCode)
            'SS = SS & Val(DGVRecipe(R, "Product_Id"))
            'SS = SS & DGVRecipe(R, "Product_Code")
            'SS = SS & DGVRecipe(R, "Product_Name")
            'SS = SS & Val(DGVRecipe(R, "Recipe_Quantity"))
            'SS = SS & Val(DGVRecipe(R, "Recipe_Quantity"))
            'SS = SS & AvCost
            'SS = SS & Totals
            'SS = SS & CostCenterID
            'SS = SS & CostCenterName
            'SS = SS & CostCenterID
            'SS = SS & CostCenterName
            'SS = SS & PatchN
            'SS = SS & SqllPatch
            'SS = SS & DGVRecipe(R, "IsExpire")
            'SS = SS & Val(DGVRecipe(R, "Unt_Id"))
            'SS = SS & DGVRecipe(R, "Unt_Name")
            'SS = SS & Val(DGVRecipe(R, "Unt_GroupId"))
            'SS = SS & Val(DGVRecipe(R, "Unt_Q"))
            'SS = SS & Val(DGVRecipe(R, "Current_Unt_Id"))
            'SS = SS & DGVRecipe(R, "Current_Unt_Name")
            'SS = SS & Val(DGVRecipe(R, "Current_Unt_Q"))
            'SS = SS & Convert.ToDouble(DGVRecipe(R, "NetQ_Qsetup_CurrentQ"))
            'SS = SS & DTP.Value
            'SS = SS & PatchTrans
            'SS = SS & DTP.Value
            'SS = SS & AvCost
            'SS = SS & Convert.ToDouble(DGVRecipe(R, "Required_Quantity"))
            'SS = SS & Convert.ToDouble(DGVRecipe(R, "UsedQuantity"))

            IFC_Cls.Details_Save(Val(TransCode), Val(DGVRecipe(R, "Product_Id")), DGVRecipe(R, "Product_Code"), DGVRecipe(R, "Product_Name"), 0, Val(DGVRecipe(R, "Recipe_Quantity")), Val(DGVRecipe(R, "Recipe_Quantity")), 0, AvCost, Totals, CostCenterID, CostCenterName, CostCenterID, CostCenterName, 0, "", 0, "", False, False, PatchN, SqllPatch, DGVRecipe(R, "IsExpire"), Val(DGVRecipe(R, "Unt_Id")), DGVRecipe(R, "Unt_Name"), Val(DGVRecipe(R, "Unt_GroupId")), Val(DGVRecipe(R, "Unt_Q")), Val(DGVRecipe(R, "Current_Unt_Id")), DGVRecipe(R, "Current_Unt_Name"), Val(DGVRecipe(R, "Current_Unt_Q")), Convert.ToDouble(DGVRecipe(R, "NetQ_Qsetup_CurrentQ")), DGvSCM(RO, "Transaction_Date_Create"), False, True, False, False, PatchTrans, False, DGvSCM(RO, "Transaction_Date_Create"), False, AvCost, Convert.ToDouble(DGVRecipe(R, "Required_Quantity")), Convert.ToDouble(DGVRecipe(R, "UsedQuantity")), Val(DGVRecipe(R, "Recipe_Product_Id")))
        Next
        'Else

        'ClsTransfer.Head_Update(Val(TxtTransaction_CodeTransfer.Text), Comp_CostCenter.SelectedValue, 0, Comp_CostCenter.Text, TxtRemarks.Text, Val(TxtTransaction_CodeTransfer.Text), Convert.ToDouble(TxtTotal.Text), Convert.ToDouble(TxtTax.Text), Convert.ToDouble(TxtNetTotal.Text), Saving, Submting, Val(Frm_Main.TxtUserId.Text), Val(AuthId), 0, Convert.ToDouble(TxtNetTotal.Text), Autholizing, DTP.Value, Date.Now, TxtTransaction_Patch.Text, userIdSubmit, False, 0)


        'For R As Integer = 1 To DGVTransfer.Rows.Count - 2
        '    If DGVTransfer(R, "Patch_Name").ToString() <> "" Then
        '        SqllPatch = DGVTransfer(R, "Patch_Name").ToString()
        '        PatchN = Val(DGVTransfer(R, "Patch_Ser"))

        '    Else
        '        SqllPatch = ""
        '        PatchN = 0
        '    End If
        '    ClsTransfer.Details_Save(Val(TxtTransaction_CodeTransfer.Text), Val(DGVTransfer(R, "Product_Id")), DGVTransfer(R, "Product_Code"), DGVTransfer(R, "Product_Name"), Val(DGVTransfer(R, "Order_Q")), Val(DGVTransfer(R, "Reciving_Q")), Val(DGVTransfer(R, "Invoice_Q")), Val(DGVTransfer(R, "Return_Q")), Convert.ToDouble(DGVTransfer(R, "Cost_Product")), Convert.ToDouble(DGVTransfer(R, "CostTotalLine")), Comp_CostCenter.SelectedValue, Comp_CostCenter.Text, Val(DGVTransfer(R, "CostCenter_Id_To")), DGVTransfer(R, "CostCenter_Name_To"), 0, "", 0, "", False, False, PatchN, SqllPatch, DGVTransfer(R, "IsExpire"), Val(DGVTransfer(R, "Unt_Id")), DGVTransfer(R, "Unt_Name"), Val(DGVTransfer(R, "Unt_GroupId")), Val(DGVTransfer(R, "Unt_Q")), Val(DGVTransfer(R, "Current_Unt_Id")), DGVTransfer(R, "Current_Unt_Name"), Val(DGVTransfer(R, "Current_Unt_Q")), Convert.ToDouble(DGVTransfer(R, "NetQ_Qsetup_CurrentQ")), DGVTransfer(R, "Transaction_Date_Create"), Saving, Submting, False, False, TxtTransaction_Patch.Text, Autholizing)
        'Next
        'End If



        'If Val(TxtTransaction_CodeTransfer.Text) > 0 Then
        '    ClsOrders.Head_UpdateAceept(Val(TxtTransaction_Code.Text), Val(Frm_Main.TxtUserId.Text))
        'End If
        '   ClsPatch.UpdateUse_Uncheck()


        'Dim Act As String = "Submit"
        'Dim ClsUserLog As New Cls_UserLog

        'Dim transs As String = Transaction_Code.ToString()
        'ScreenName = Me.Text
        'ClsUserLog.Save_UserLog(Date.Now, Act, ScreenName, Val(Frm_Main.TxtUserId.Text), UserName, Comp_CostCenter.Text, transs, Comp_CostCenter.Text, transs, TxtRemarks.Text)

        'Dt.Clear()
        'Dt = Clsmsg.SelectMsg("Save")
        'MessageBox.Show(Dt.Rows(0)(1).ToString(), Dt.Rows(0)(2).ToString(), MessageBoxButtons.OK, MessageBoxIcon.Information)


        'Dim result As DialogResult = MsgBox("Production ,Do You Want To Print It ?", 1 + 32 + 0 + 0, "Production Confirmation")
        'Dim conn As New CLS_Conn
        'If result = System.Windows.Forms.DialogResult.OK Then
        '    conn.OpenConnection()
        '    Dim ad As New SqlDataAdapter("select * from ProductionMaster where Transaction_Patch='" & Transaction_Patch & "' and Transaction_Code=" & Transaction_Code & " ", conn.SqlConn)
        '    Dim dss As New DataSet
        '    ad.Fill(dss, "ProductionMaster")
        '    Dim ada As New SqlDataAdapter("select * from ProductionDetails where Transaction_Patch='" & Transaction_Patch & "'  and Transaction_Code=" & Transaction_Code & "   and Transaction_Id=7", conn.SqlConn)

        '    ada.Fill(dss, "ProductionDetails")

        '    '  Dim dv As New DataView(dss.Tables("ProductionMaster"))

        '    Dim rpt As CrystalDecisions.CrystalReports.Engine.ReportDocument
        '    rpt = New Productions
        '    rpt.SetDataSource(dss)
        '    FrmReporst.CrystalReportViewer1.ReportSource = rpt
        '    FrmReporst.CrystalReportViewer1.RefreshReport()
        '    FrmReporst.ShowDialog()
        'End If
        'Clears()
    End Sub
    Public Function RowEffectQuantity(RO As Integer, Optional Ched As Boolean = False) As Boolean

        'On Error Resume Next
        Dim noacc As Boolean
        Dim CC As String
        Dim Dt, DTPatch As New DataTable
        Dim TotAvCost As Double = 0
        'CC = DGvSCM.Cols(DGVProduction.ColSel).Name

        If DGVRecipe.Rows.Count - 1 = 0 Then
            noacc = False
            Return noacc
        End If
        '  If CC = "Reciving_Q" Then
        'If IsDBNull(DGVProduction(RO, "Reciving_Q")) Or (DGVRecipe(DGVRecipe.RowSel, "Reciving_Q")).ToString = "" Then DGVRecipe(DGVRecipe.RowSel, "Reciving_Q") = 0
        For L As Integer = 0 To DGVRecipe.Rows.Count - 1
            noacc = False
            If Val(DGVRecipe(L, "Recipe_Product_Id")) = Val(DGVProduction(DGVProduction.Rows.Count - 2, "Product_Id")) Then
                Dim Rec, Req, Usd, RecQ, soht, uq As Decimal
                Rec = DGVProduction(DGVProduction.Rows.Count - 2, "Reciving_Q")
                DGVRecipe(L, "Required_Quantity") = DGVProduction(DGVProduction.Rows.Count - 2, "Reciving_Q")
                'DGVRecipe(L, "Recipe_Quantity") = Math.Round(Val(DGVRecipe(L, "UsedQuantity")) * Val(DGVRecipe(L, "Required_Quantity")), 2)
                Usd = Val(DGVRecipe(L, "UsedQuantity"))
                uq = Val(DGVRecipe(L, "Unt_Q"))
                Req = Val(DGVRecipe(L, "Required_Quantity"))
                RecQ = Val(DGVRecipe(L, "Recipe_Quantity"))

                RecQ = Val(DGVRecipe(L, "UsedQuantity")) * Val(DGVRecipe(L, "Current_Unt_Q"))

                '    DGVRecipe(L, "Recipe_Quantity") = Val(RecQ) / Val(DGVRecipe(L, "Unt_Q")) ', 2)
                DGVRecipe(L, "Recipe_Quantity") = Val(RecQ) * Val(DGVRecipe(L, "Required_Quantity")) ', 2)

                '  RecQ = Val(RecQ) / Val(DGVRecipe(L, "Unt_Q")) ', 2)
                RecQ = Val(RecQ) * Val(DGVRecipe(L, "Required_Quantity")) ', 2)
                DGVRecipe(L, "Recipe_Quantity") = RecQ

                Dim QLast As Decimal = 0
                If Ched Then QLast = IFC_Cls.GetQuantityNow(CInt(DGVRecipe(L, "Product_Id")), CInt(DGvSCM(RO, "CostCenter_Id_To")), DGVRecipe(L, "Product_Code").ToString(), CDec(DGVRecipe(L, "Recipe_Quantity")))

                DGVRecipe(L, "Shortage") = Val(DGVRecipe(L, "Stock_On_Hand")) - (Val(DGVRecipe(L, "Recipe_Quantity")) + QLast)
                DGVRecipe(L, "NetQ_Qsetup_CurrentQ") = Convert.ToDouble(RecQ) / Convert.ToDouble(DGVRecipe(L, "Unt_Q")) ', 2)' Val(DGVRecipe(L, "Recipe_Quantity"))

                Req = Val(DGVRecipe(L, "Required_Quantity"))
                RecQ = Val(DGVRecipe(L, "Recipe_Quantity"))

                DGVRecipe(L, "CostTotalLine") = Math.Round(Val(DGVRecipe(L, "Recipe_Quantity")) * Val(DGVRecipe(L, "Cost_Product")), 2)
                TotAvCost += Convert.ToDouble(DGVRecipe(L, "Cost_Product"))
            End If

        Next
        Dim NewAv, TotNewQ As Double
        TotAvCost = Val(TotAvCost) * Val(DGVProduction(DGVProduction.Rows.Count - 2, "Reciving_Q"))
        DGVProduction(DGVProduction.Rows.Count - 2, "CostTotalLine") = Math.Round(TotAvCost, 2)
        NewAv = Val(DGVProduction(DGVProduction.Rows.Count - 2, "InStock")) * Val(DGVProduction(DGVProduction.Rows.Count - 2, "OldCost_Product"))
        TotNewQ = Val(DGVProduction(DGVProduction.Rows.Count - 2, "InStock")) + Val(DGVProduction(DGVProduction.Rows.Count - 2, "Reciving_Q"))

        TotAvCost += NewAv
        TotAvCost = Math.Round(TotAvCost / TotNewQ, 2)
        DGVProduction(DGVProduction.Rows.Count - 2, "NetQ_Qsetup_CurrentQ") = Val(DGVProduction(DGVProduction.Rows.Count - 2, "Reciving_Q"))
        DGVProduction(DGVProduction.Rows.Count - 2, "OldCost_Product") = TotAvCost

        '' MessageBox.Show(Val(DGVProduction(DGVProduction.RowSel, "Reciving_Q")))
        'TotAvCost = Math.Round(Val(TotAvCost) / Val(DGVProduction(DGVProduction.RowSel, "Reciving_Q")), 2)
        'DGVProduction(DGVProduction.RowSel, "Cost_Product") = TotAvCost
        'DGVProduction(DGVProduction.RowSel, "NetQ_Qsetup_CurrentQ") = Val(DGVProduction(DGVProduction.RowSel, "Reciving_Q"))
        'DGVProduction(DGVProduction.RowSel, "CostTotalLine") = Math.Round(Val(DGVProduction(DGVProduction.RowSel, "Reciving_Q")) * Val(DGVProduction(DGVProduction.RowSel, "Cost_Product")), 2)
        ' End If

        For su As Integer = 0 To DGVRecipe.Rows.Count - 1
            If Val(DGVRecipe(su, "Shortage")) < 0 Then
                noacc = False
                Exit For

            Else
                noacc = True
            End If
        Next
        Return noacc
    End Function
    Public Sub GetProductionRecipyData(Ro As Integer)
        Dim Dt, DTPatch As New DataTable

        If DGVProduction.RowSel = 1 Then
            InitializeTempProductionTable()
        End If

        DGVProduction(DGVProduction.RowSel, "Product_Code") = DGvSCM(Ro, "Product_Code")
        DGVProduction(DGVProduction.RowSel, "Product_Name") = DGvSCM(Ro, "Product_Name")
        DGVProduction(DGVProduction.RowSel, "Unt_Id") = DGvSCM(Ro, "Unt_Id")
        DGVProduction(DGVProduction.RowSel, "Unt_Name") = DGvSCM(Ro, "Unt_Name")
        DGVProduction(DGVProduction.RowSel, "Unt_GroupId") = DGvSCM(Ro, "Unt_GroupId")
        DGVProduction(DGVProduction.RowSel, "Unt_Q") = DGvSCM(Ro, "Unt_Q")
        DGVProduction(DGVProduction.RowSel, "Current_Unt_Id") = DGvSCM(Ro, "Unt_Id")
        DGVProduction(DGVProduction.RowSel, "Current_Unt_Name") = DGvSCM(Ro, "Unt_Name")
        DGVProduction(DGVProduction.RowSel, "Current_Unt_Q") = DGvSCM(Ro, "Unt_Q")
        DGVProduction(DGVProduction.RowSel, "Cost_Product") = Math.Round(DGvSCM(Ro, "Cost_Product"), 2)
        DGVProduction(DGVProduction.RowSel, "OldCost_Product") = Math.Round(DGvSCM(Ro, "Cost_Product"), 2)
        DGVProduction(DGVProduction.RowSel, "Reciving_Q") = Math.Abs(DGvSCM(Ro, "Reciving_Q"))
        DGVProduction(DGVProduction.RowSel, "CostTotalLine") = 0
        DGVProduction(DGVProduction.RowSel, "IsExpire") = DGvSCM(Ro, "IsExpire")
        DGVProduction(DGVProduction.RowSel, "Product_Id") = DGvSCM(Ro, "Product_Id")
        DGVProduction(DGVProduction.RowSel, "NetQ_Qsetup_CurrentQ") = Math.Abs(DGvSCM(Ro, "Reciving_Q"))

        Dim productionStockQty As Double = GetAvailableQuantityWithProduction(Val(DGVProduction(DGVProduction.RowSel, "Product_Id")), Val(DGvSCM(Ro, "CostCenter_Id_To")))
        DGVProduction(DGVProduction.RowSel, "InStock") = productionStockQty

        If DGVProduction(DGVProduction.RowSel, "IsExpire").ToString() = "True" Then
            Dt.Clear()
            Dt = IFC_Cls.ShowProductIdExpireDate_(Val(DGVProduction(DGVProduction.RowSel, "Product_Id")), Val(DGvSCM(Ro, "CostCenter_Id_To")))
            If Dt.Rows.Count = 0 Then
                DGVProduction(DGVProduction.RowSel, "Patch_Ser") = 0
                DGVProduction(DGVProduction.RowSel, "Patch_Name") = ""
            Else
                DGVProduction(DGVProduction.RowSel, "Patch_Ser") = Dt.Rows(0)("Patch_Ser")
                DGVProduction(DGVProduction.RowSel, "Patch_Name") = Dt.Rows(0)("Patch_Name")
            End If
        End If

        Dt.Clear()
        Dt = IFC_Cls.ShowWithProduct_IdAll(Val(DGVProduction(DGVProduction.RowSel, "Product_Id")))

        For R As Integer = 0 To Dt.Rows.Count - 1
            DGVRecipe.Rows.Add()

            DGVRecipe(DGVRecipe.Rows.Count - 1, "Product_Code") = Dt.Rows(R)("Recipe_Product_Code")
            DGVRecipe(DGVRecipe.Rows.Count - 1, "Product_Name") = Dt.Rows(R)("Recipe_Product_Name")
            DGVRecipe(DGVRecipe.Rows.Count - 1, "Unt_Name") = Dt.Rows(R)("Unt_Name")
            DGVRecipe(DGVRecipe.Rows.Count - 1, "UsedQuantity") = Dt.Rows(R)("UsedQuantity")
            DGVRecipe(DGVRecipe.Rows.Count - 1, "Required_Quantity") = 0
            DGVRecipe(DGVRecipe.Rows.Count - 1, "Recipe_Quantity") = 0
            DGVRecipe(DGVRecipe.Rows.Count - 1, "Unt_Id") = Dt.Rows(R)("Unt_Id")
            DGVRecipe(DGVRecipe.Rows.Count - 1, "Unt_GroupId") = Dt.Rows(R)("Unt_GroupId")
            DGVRecipe(DGVRecipe.Rows.Count - 1, "Unt_Q") = Dt.Rows(R)("Unt_Q")
            DGVRecipe(DGVRecipe.Rows.Count - 1, "Current_Unt_Id") = Dt.Rows(R)("Current_Unt_Id")
            DGVRecipe(DGVRecipe.Rows.Count - 1, "Current_Unt_Name") = Dt.Rows(R)("Current_Unt_Name")
            DGVRecipe(DGVRecipe.Rows.Count - 1, "Current_Unt_Q") = Dt.Rows(R)("Current_Unt_Q")
            DGVRecipe(DGVRecipe.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = Dt.Rows(R)("NetQ_Qsetup_CurrentQ")
            DGVRecipe(DGVRecipe.Rows.Count - 1, "Recipe_Product_Id") = Dt.Rows(R)("Product_Id")
            DGVRecipe(DGVRecipe.Rows.Count - 1, "Product_Id") = Dt.Rows(R)("Recipe_Product_Id")
            DGVRecipe(DGVRecipe.Rows.Count - 1, "CostTotalLine") = 0
            DGVRecipe(DGVRecipe.Rows.Count - 1, "Cost_Product") = Dt.Rows(R)("Recipe_Lost_cost")

            Dim availableQty As Double = GetAvailableQuantityWithProduction(Val(DGVRecipe(DGVRecipe.Rows.Count - 1, "Product_Id")), Val(DGvSCM(Ro, "CostCenter_Id_To")))
            DGVRecipe(DGVRecipe.Rows.Count - 1, "Stock_On_Hand") = availableQty

            DTPatch.Clear()
            DTPatch = IFC_Cls.ShowDataParametars_(" Product_Id=" & Val(DGVRecipe(DGVRecipe.Rows.Count - 1, "Product_Id")) & "")

            If DTPatch.Rows.Count > 0 Then
                DGVRecipe(DGVRecipe.Rows.Count - 1, "IsExpire") = DTPatch.Rows(0)("IsExpire")

                If DGVRecipe(DGVRecipe.Rows.Count - 1, "IsExpire").ToString() = "True" Then
                    DTPatch.Clear()
                    DTPatch = IFC_Cls.ShowProductIdExpireDate_(Val(DGVRecipe(DGVRecipe.Rows.Count - 1, "Product_Id")), Val(DGvSCM(Ro, "CostCenter_Id_To")))
                    If DTPatch.Rows.Count = 0 Then
                        DGVRecipe(DGVRecipe.Rows.Count - 1, "Patch_Ser") = 0
                        DGVRecipe(DGVRecipe.Rows.Count - 1, "Patch_Name") = ""
                    Else
                        DGVRecipe(DGVRecipe.Rows.Count - 1, "Patch_Ser") = DTPatch.Rows(0)("Patch_Ser")
                        DGVRecipe(DGVRecipe.Rows.Count - 1, "Patch_Name") = DTPatch.Rows(0)("Patch_Name")
                    End If
                End If
            Else
                DGVRecipe(DGVRecipe.Rows.Count - 1, "IsExpire") = "False"
            End If

            DGVRecipe(DGVRecipe.Rows.Count - 1, "Shortage") = Val(DGVRecipe(DGVRecipe.Rows.Count - 1, "Stock_On_Hand")) - Val(DGVRecipe(DGVRecipe.Rows.Count - 1, "Recipe_Quantity"))
        Next

        '///////////////////////////////////////////////////////////
        Dim Totline As Double = 0
        For tt As Integer = 1 To DGVRecipe.Rows.Count - 1
            If Val(DGVRecipe(tt, "Recipe_Product_Id")) = Val(DGVProduction(DGVProduction.RowSel, "Product_Id")) Then
                Totline += Val(DGVRecipe(tt, "Cost_Product"))
            End If

        Next
        DGVProduction(DGVProduction.RowSel, "Cost_Product") = Math.Round(Totline, 2) 'Math.Round(f.DGVProduct(f.Innt, "Cost_Product"), 2)
        '  MessageBox.Show(DGVProduction(DGVProduction.RowSel, "OldCost_Product"))
        DGVProduction(DGVProduction.RowSel, "OldCost_Product") = Totline
        'TotAvCost = Val(TotAvCost) * Val(DGVProduction(DGVProduction.Rows.Count - 2, "Reciving_Q"))
        DGVProduction(DGVProduction.RowSel, "CostTotalLine") = Totline * Val(DGVProduction(DGVProduction.RowSel, "Reciving_Q")) 'Math.Round(TotAvCost, 2)

        DGVProduction.Rows.Add()
    End Sub

    ''' <summary>
    ''' Initializes temporary production table to track what's being produced in this transaction
    ''' </summary>
    Private Sub InitializeTempProductionTable()
        Try
            ' Drop table if it exists
            Conn.EXECUT_Txt("IF OBJECT_ID('tempdb..#TempProduction') IS NOT NULL DROP TABLE #TempProduction")

            ' Create temporary table
            Dim createTableSql As String = "
                CREATE TABLE #TempProduction (
                    ProductId INT,
                    QuantityProduced DECIMAL(18,2),
                    ProductCode NVARCHAR(50),
                    ProductName NVARCHAR(255)
                )"
            Conn.EXECUT_Txt(createTableSql)

            ' Populate with all production items from current transaction
            For scmRow As Integer = 0 To DGvSCM.Rows.Count - 1
                ' Safe Boolean conversion
                Dim isProduction As Boolean = False
                Try
                    Dim prodValue = DGvSCM(scmRow, "Is_Production")
                    If prodValue IsNot Nothing Then
                        If TypeOf prodValue Is Boolean Then
                            isProduction = DirectCast(prodValue, Boolean)
                        ElseIf TypeOf prodValue Is String Then
                            isProduction = (prodValue.ToString().ToLower() = "true" Or prodValue.ToString() = "1")
                        Else
                            isProduction = (Val(prodValue.ToString()) <> 0)
                        End If
                    End If
                Catch
                    isProduction = False
                End Try

                If isProduction Then
                    Dim productId As Integer = Val(DGvSCM(scmRow, "Product_Id"))
                    Dim quantity As Double = Math.Abs(Val(DGvSCM(scmRow, "Reciving_Q")))
                    Dim productCode As String = DGvSCM(scmRow, "Product_Code").ToString()
                    Dim productName As String = DGvSCM(scmRow, "Product_Name").ToString()

                    Dim insertSql As String = String.Format(
                        "INSERT INTO #TempProduction (ProductId, QuantityProduced, ProductCode, ProductName) VALUES ({0}, {1}, '{2}', '{3}')",
                        productId, quantity, productCode.Replace("'", "''"), productName.Replace("'", "''"))
                    Conn.EXECUT_Txt(insertSql)
                End If
            Next

        Catch ex As Exception
            ' If temp table creation fails, continue with normal processing
            MessageBox.Show("Warning: Could not create temporary production table. Processing will continue normally. Error: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' Gets available quantity considering both physical stock and pending production
    ''' </summary>
    ''' <param name="productId">Product ID to check</param>
    ''' <param name="costCenterId">Cost Center ID</param>
    ''' <returns>Available quantity including pending production</returns>
    Private Function GetAvailableQuantityWithProduction(productId As Integer, costCenterId As Integer) As Double
        Try
            Dim sql As String = "
                SELECT
                    ISNULL(soh.Quntity, 0) * ISNULL(soh.Item_Unit, 1) AS PhysicalStock,
                    ISNULL(temp.QuantityProduced, 0) AS PendingProduction,
                    (ISNULL(soh.Quntity, 0) * ISNULL(soh.Item_Unit, 1) + ISNULL(temp.QuantityProduced, 0)) AS TotalAvailable
                FROM (SELECT " & productId & " AS ProductId) p
                LEFT JOIN StockOnHandTbl soh ON p.ProductId = soh.Product_Id AND soh.CostCenter_Id = " & costCenterId & "
                LEFT JOIN #TempProduction temp ON p.ProductId = temp.ProductId"

            Dim dt As DataTable = Conn.SELECT_TXT(sql)
            If dt.Rows.Count > 0 Then
                Return Convert.ToDouble(dt.Rows(0)("TotalAvailable"))
            Else
                Return 0
            End If
        Catch ex As Exception
            ' If temp table query fails, fall back to normal stock check
            Dim dt As DataTable = ClsStock.Show_StockWithParam(productId, costCenterId)
            If dt.Rows.Count > 0 Then
                Return Convert.ToDouble(dt.Rows(0)("Quntity")) * Convert.ToDouble(dt.Rows(0)("Item_Unit"))
            Else
                Return 0
            End If
        End Try
    End Function

    Private Sub Grids_Bulid_prod()
        'DGVProduction.Rows.Count = 1
        'DGVProduction.Cols.Count = 17
        'DGVProduction.Rows.Count = 2
        ' DGVRecipe.Cols.Count = 13
        DGVProduction.Rows(0).Height = 30

        DGVProduction.DataSource = Nothing
        DGVProduction.DataSource = Dttble()


        DGVProduction.Cols("Product_Code").Caption = "Product Code"
        DGVProduction.Cols("Product_Name").Caption = "Product Name"
        DGVProduction.Cols("Reciving_Q").Caption = "Recipe Qty"
        DGVProduction.Cols("InStock").Caption = "In Stock"
        DGVProduction.Cols("Cost_Product").Caption = "Cost %"
        DGVProduction.Cols("OldCost_Product").Visible = False
        'DGVProduction.Cols(0).Name = "Product_Code"
        'DGVProduction.Cols(1).Name = "Product_Name"
        'DGVProduction.Cols(2).Name = "Reciving_Q"
        'DGVProduction.Cols(3).Name = "InStock"
        'DGVProduction.Cols(4).Name = "Cost_Product"
        'DGVProduction.Cols(5).Name = "Patch_Ser"
        'DGVProduction.Cols(6).Name = "Patch_Name"
        'DGVProduction.Cols(7).Name = "IsExpire"
        'DGVProduction.Cols(8).Name = "Unt_Id"
        'DGVProduction.Cols(9).Name = "Unt_Name"
        'DGVProduction.Cols(10).Name = "Unt_GroupId"
        'DGVProduction.Cols(11).Name = "Unt_Q"
        'DGVProduction.Cols(12).Name = "Current_Unt_Id"
        'DGVProduction.Cols(13).Name = "Current_Unt_Name"
        'DGVProduction.Cols(14).Name = "Current_Unt_Q"
        'DGVProduction.Cols(15).Name = "NetQ_Qsetup_CurrentQ"
        'DGVProduction.Cols(16).Name = "CostTotalLine"
        'DGVProduction.Cols(0).Name = ""

        'DGVProduction.Cols(1).AllowEditing = False

        'DGVProduction.Cols(3).AllowEditing = False
        'DGVProduction.Cols(4).AllowEditing = True


        Dim MWidthd As Integer = DGVProduction.Width
        DGVProduction.Cols(0).Width = Val(MWidthd) * Val(0.2)
        DGVProduction.Cols(1).Width = Val(MWidthd) * Val(0.3)
        DGVProduction.Cols(2).Width = Val(MWidthd) * Val(0.15)
        DGVProduction.Cols(3).Width = Val(MWidthd) * Val(0.15)
        DGVProduction.Cols(4).Width = Val(MWidthd) * Val(0.2)
        '=============================================
        DGVRecipe.DataSource = Nothing
        DGVRecipe.DataSource = DtRecipy()

        DGVRecipe.Cols("Product_Code").Caption = "Product Code"
        DGVRecipe.Cols("Product_Name").Caption = "Product Name"
        DGVRecipe.Cols("Current_Unt_Name").Caption = "Recipe Unit Size"
        DGVRecipe.Cols("UsedQuantity").Caption = "Unit of Measure"
        DGVRecipe.Cols("Required_Quantity").Caption = "Required Quantity"
        DGVRecipe.Cols("Recipe_Quantity").Caption = "Recipe Stock"
        DGVRecipe.Cols("Stock_On_Hand").Caption = "Stock On Hand"
        DGVRecipe.Cols("Shortage").Caption = "Shortage"
        DGVRecipe.Cols("Recipe_Product_Id").Caption = "ItemCodRecipy"
        'DGVRecipe.Cols(9).Caption = "u_z"
        'DGVRecipe.Cols(10).Caption = "unt"
        'DGVRecipe.Cols(11).Caption = "Curru_z"
        'DGVRecipe.Cols(12).Caption = "rlt"


        '   DGVProduction.Cols.Count = 5

        DGVRecipe.Rows(0).Height = 30
        DGVRecipe.Rows.Count = 1
        Dim MWidth As Integer = DGVRecipe.Width
        DGVRecipe.Cols(0).Width = Val(MWidth) * Val(0.09)
        DGVRecipe.Cols(1).Width = Val(MWidth) * Val(0.2)
        DGVRecipe.Cols(2).Width = Val(MWidth) * Val(0.1)
        DGVRecipe.Cols(3).Width = Val(MWidth) * Val(0.1)
        DGVRecipe.Cols(4).Width = Val(MWidth) * Val(0.1)
        DGVRecipe.Cols(5).Width = Val(MWidth) * Val(0.1)
        DGVRecipe.Cols(6).Width = Val(MWidth) * Val(0.2)
        DGVRecipe.Cols(7).Width = Val(MWidth) * Val(0.1)

        'DGVRecipe.Cols(8).Visible = False
        'DGVRecipe.Cols(9).Visible = False
        'DGVRecipe.Cols(10).Visible = False
        'DGVRecipe.Cols(11).Visible = False
        'DGVRecipe.Cols(12).Visible = False
        For i As Integer = 8 To DGVRecipe.Cols.Count - 1
            DGVRecipe.Cols(i).Visible = False
        Next
        '///////////////// Allow Edit False

        For i As Integer = 0 To DGVRecipe.Cols.Count - 1
            DGVRecipe.Cols(i).AllowEditing = False
        Next


        For i As Integer = 0 To DGVProduction.Cols.Count - 1
            DGVProduction.Cols(i).AllowEditing = False
        Next

        DGVProduction.Cols(2).AllowEditing = True

        Call Setup_Flex_prod()
        ' Btn_Submit.Enabled = False
        DGVProduction.AllowSorting = AllowSortingEnum.None
        DGVRecipe.AllowSorting = AllowSortingEnum.None
    End Sub
    Private Sub Setup_Flex_prod()
        Dim SS As Integer
        '  SS = FG_Clims.RowSel
        Dim rc As CellRange = DGVProduction.GetCellRange(SS, 6)
        Dim cs As CellStyle
        cs = DGVProduction.Styles.Add("emp")
        'cs.DataType = GetType(String)
        ''FG_Prod1.Cols(6).DataType = GetType(String)
        'If Cn.State = ConnectionState.Open Then Cn.Close()
        'Cn.Open()
        'cmd.Connection = Cn
        'cmd.CommandType = CommandType.Text
        'cmd.CommandText = "select ser,nam from con_tree_Costcenter order by nam "
        'Dim dread As SqlDataReader
        ''cmd.ExecuteNonQuery()
        'dread = cmd.ExecuteReader()
        'Dim Cost As String
        'Cost = ""
        'While dread.Read
        '    Cost = Cost & "|" & dread("nam")
        'End While
        'FG_Prod1.Cols(9).DataType = GetType(String)
        'FG_Prod1.Cols(9).ComboList = Cost



        cs = DGVProduction.Styles.Add("Number")
        cs.DataType = GetType(Double)
        cs.ImageAlign = ImageAlignEnum.CenterCenter
        '' rc = FG_PR.Cols(3).Style
        DGVProduction.Cols(2).Style = DGVProduction.Styles("Number")
        'Cn.Close()
        'For O = 0 To 9
        '    FG_Prod2.Cols(0).AllowEditing = False
        'Next

        'For b = 0 To 4
        '    FG_Prod1.Cols(b).AllowEditing = False
        'Next
        'FG_Prod1.Cols(2).AllowEditing = True
    End Sub
    Public Function Dttble() As DataTable

        Dim Dt As New DataTable("Productions")
        ' Dt.Clear()

        'DGVProduction.Cols(0).Name = ""
        'DGVProduction.Cols(1).Name = ""
        'DGVProduction.Cols(2).Name = ""
        'DGVProduction.Cols(3).Name = ""
        'DGVProduction.Cols(4).Name = ""
        'DGVProduction.Cols(5).Name = ""
        'DGVProduction.Cols(6).Name = ""
        'DGVProduction.Cols(7).Name = ""
        'DGVProduction.Cols(8).Name = ""
        'DGVProduction.Cols(9).Name = ""
        'DGVProduction.Cols(10).Name = ""
        'DGVProduction.Cols(11).Name = ""
        'DGVProduction.Cols(12).Name = ""
        'DGVProduction.Cols(13).Name = ""
        'DGVProduction.Cols(14).Name = ""
        'DGVProduction.Cols(15).Name = ""
        'DGVProduction.Cols(16).Name = ""

        Dt.Columns.Add("Product_Code", GetType(String))
        Dt.Columns.Add("Product_Name", GetType(String))
        Dt.Columns.Add("Reciving_Q", GetType(Double))
        Dt.Columns.Add("InStock", GetType(Double))
        Dt.Columns.Add("Cost_Product", GetType(Double))
        Dt.Columns.Add("Patch_Ser", GetType(Integer))
        Dt.Columns.Add("Patch_Name", GetType(String))
        Dt.Columns.Add("IsExpire", GetType(Boolean))
        Dt.Columns.Add("Unt_Id", GetType(Integer))
        Dt.Columns.Add("Unt_Name", GetType(String))
        Dt.Columns.Add("Unt_GroupId", GetType(Integer))
        Dt.Columns.Add("Unt_Q", GetType(Double))
        Dt.Columns.Add("Current_Unt_Id", GetType(Integer))
        Dt.Columns.Add("Current_Unt_Name", GetType(String))
        Dt.Columns.Add("Current_Unt_Q", GetType(Double))
        Dt.Columns.Add("NetQ_Qsetup_CurrentQ", GetType(Double))
        Dt.Columns.Add("CostTotalLine", GetType(Double))
        Dt.Columns.Add("Product_Id", GetType(Integer))
        Dt.Columns.Add("OldCost_Product", GetType(Double))
        Dt.Rows.Add()
        Return Dt

    End Function
    'Public Sub FillRecipyProduction()
    '    Dim CC As String
    '    Dim Dt, DTPatch As New DataTable

    '    CC = DGVProduction.Cols(DGVProduction.ColSel).Name
    '    If CC = "Product_Code" Or CC = "Product_Name" Then

    '        If DGVProduction(DGVProduction.RowSel, DGVProduction.ColSel).ToString() <> "" Then Return
    '        If e.KeyChar = ChrW(Keys.Enter) Then

    '            If Comp_CostCenter.SelectedIndex = -1 Then
    '                MessageBox.Show("Please Choose The Cost Center First", "Warrning", MessageBoxButtons.OK, MessageBoxIcon.Error)
    '                Comp_CostCenter.Focus()
    '                Return
    '            End If
    '            Dim f As New FindItems_Frm("Production")

    '            f.ShowDialog()
    '            If f.IsSelect = True Then


    '                For R As Integer = 1 To DGVProduction.Rows.Count - 2
    '                    DGVProduction.Rows(R).Selected = False
    '                    If DGVProduction(R, "Product_Id").ToString() = f.DGVProduct(f.Innt, "Product_Id").ToString() Then
    '                        '   Dim Dt As New DataTable
    '                        Dt.Clear()
    '                        Dt = Clsmsg.SelectMsg("AddBefore")
    '                        MessageBox.Show(Dt.Rows(0)(1).ToString(), Dt.Rows(0)(2).ToString(), MessageBoxButtons.OK, MessageBoxIcon.Error)

    '                        DGVProduction.Rows(R).Selected = True
    '                        Return
    '                    End If
    '                Next


    '                DGVProduction(DGVProduction.RowSel, "Product_Code") = f.DGVProduct(f.Innt, "Product_Code")
    '                DGVProduction(DGVProduction.RowSel, "Product_Name") = f.DGVProduct(f.Innt, "Product_Name")
    '                DGVProduction(DGVProduction.RowSel, "Unt_Id") = f.DGVProduct(f.Innt, "Unt_Id")
    '                DGVProduction(DGVProduction.RowSel, "Unt_Name") = f.DGVProduct(f.Innt, "Unt_Name")
    '                DGVProduction(DGVProduction.RowSel, "Unt_GroupId") = f.DGVProduct(f.Innt, "Unt_GroupId")
    '                DGVProduction(DGVProduction.RowSel, "Unt_Q") = f.DGVProduct(f.Innt, "Unt_Q")
    '                DGVProduction(DGVProduction.RowSel, "Current_Unt_Id") = f.DGVProduct(f.Innt, "Unt_Id")
    '                DGVProduction(DGVProduction.RowSel, "Current_Unt_Name") = f.DGVProduct(f.Innt, "Unt_Name")
    '                DGVProduction(DGVProduction.RowSel, "Current_Unt_Q") = f.DGVProduct(f.Innt, "Unt_Q")
    '                DGVProduction(DGVProduction.RowSel, "Cost_Product") = Math.Round(f.DGVProduct(f.Innt, "Cost_Product"), 2)

    '                DGVProduction(DGVProduction.RowSel, "OldCost_Product") = Math.Round(f.DGVProduct(f.Innt, "Cost_Product"), 2)
    '                DGVProduction(DGVProduction.RowSel, "Reciving_Q") = 0
    '                '  DGVProduction(DGVProduction.RowSel, "Invoice_Q") = 0
    '                ' DGVProduction(DGVProduction.RowSel, "Return_Q") = 0
    '                DGVProduction(DGVProduction.RowSel, "CostTotalLine") = 0
    '                DGVProduction(DGVProduction.RowSel, "IsExpire") = f.DGVProduct(f.Innt, "IsExpire")
    '                ' DGVProduction(DGVProduction.RowSel, "Transaction_Date_Create") = Date.Now
    '                DGVProduction(DGVProduction.RowSel, "Product_Id") = f.DGVProduct(f.Innt, "Product_Id")
    '                DGVProduction(DGVProduction.RowSel, "NetQ_Qsetup_CurrentQ") = 0
    '                '  TxtProductId.Text = f.DGVProduct(f.DGVProduct.RowSel, "")
    '                '////////////////////////////////////////////
    '                '////////////// Get Stock On Hand//////////////////////
    '                Dt.Clear()
    '                Dt = ClsStock.Show_StockWithParam(Val(DGVProduction(DGVProduction.RowSel, "Product_Id")), Comp_CostCenter.SelectedValue)
    '                If Dt.Rows.Count > 0 Then

    '                    DGVProduction(DGVProduction.RowSel, "InStock") = Dt.Rows(0)("Quntity")
    '                Else
    '                    DGVProduction(DGVProduction.RowSel, "InStock") = 0
    '                End If
    '                '//////////////////Get Patch////////////////////////////
    '                If DGVProduction(DGVProduction.RowSel, "IsExpire").ToString() = "True" Then
    '                    'Dim Dt As New DataTable
    '                    Dt.Clear()
    '                    Dt = ClsPatch.ShowProductIdExpireDate_(Val(DGVProduction(DGVProduction.RowSel, "Product_Id")), Comp_CostCenter.SelectedValue)
    '                    If Dt.Rows.Count = 0 Then
    '                        MessageBox.Show("Sorry This Cost Center Dont Have Expire Patch " & Comp_CostCenter.Text, "Warrning", MessageBoxButtons.OK, MessageBoxIcon.Error)
    '                        DGVProduction.Rows.Remove(DGVProduction.RowSel)
    '                        Btn_Clear_Click(sender, e)
    '                        Return
    '                    Else
    '                        DGVProduction(DGVProduction.RowSel, "Patch_Ser") = Dt.Rows(0)("Patch_Ser")
    '                        DGVProduction(DGVProduction.RowSel, "Patch_Name") = Dt.Rows(0)("Patch_Name")
    '                    End If

    '                End If
    '                '/////////////////////////////////get recipy data////////////
    '                Dt.Clear()
    '                Dt = ClsRecipy.ShowWithProduct_IdAll(Val(DGVProduction(DGVProduction.RowSel, "Product_Id")))

    '                For R As Integer = 0 To Dt.Rows.Count - 1
    '                    DGVRecipe.Rows.Add()

    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Product_Code") = Dt.Rows(R)("Recipe_Product_Code")
    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Product_Name") = Dt.Rows(R)("Recipe_Product_Name")
    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Unt_Name") = Dt.Rows(R)("Unt_Name")
    '                    '  DGVRecipe(DGVRecipe.Rows.Count - 1, "Current_Unt_Name") = Dt.Rows(R)("Current_Unt_Name")

    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "UsedQuantity") = Dt.Rows(R)("UsedQuantity")
    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Required_Quantity") = 0 ' Dt.Rows(R)("")
    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Recipe_Quantity") = 0



    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Unt_Id") = Dt.Rows(R)("Unt_Id")
    '                    '  DGVRecipe(DGVRecipe.Rows.Count - 1, "Unt_Name") = Dt.Rows(R)("Unt_Name")
    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Unt_GroupId") = Dt.Rows(R)("Unt_GroupId")
    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Unt_Q") = Dt.Rows(R)("Unt_Q")
    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Current_Unt_Id") = Dt.Rows(R)("Current_Unt_Id")
    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Current_Unt_Name") = Dt.Rows(R)("Current_Unt_Name")
    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Current_Unt_Q") = Dt.Rows(R)("Current_Unt_Q")
    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "NetQ_Qsetup_CurrentQ") = Dt.Rows(R)("NetQ_Qsetup_CurrentQ")
    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Recipe_Product_Id") = Dt.Rows(R)("Product_Id")
    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Product_Id") = Dt.Rows(R)("Recipe_Product_Id")
    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "CostTotalLine") = 0
    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Cost_Product") = Dt.Rows(R)("Recipe_Lost_cost")
    '                    '/////////////////stockonhand
    '                    '    MessageBox.Show(DGVRecipe(DGVRecipe.Rows.Count - 1, "Cost_Product"))
    '                    DTPatch.Clear()
    '                    DTPatch = ClsStock.Show_StockWithParam(Val(DGVRecipe(DGVRecipe.Rows.Count - 1, "Product_Id")), Comp_CostCenter.SelectedValue)
    '                    If DTPatch.Rows.Count > 0 Then

    '                        DGVRecipe(DGVRecipe.Rows.Count - 1, "Stock_On_Hand") = DTPatch.Rows(0)("Quntity")
    '                    Else
    '                        DGVRecipe(DGVRecipe.Rows.Count - 1, "Stock_On_Hand") = 0
    '                    End If
    '                    '//////////////////////////////////////
    '                    DTPatch.Clear()
    '                    DTPatch = ClsProduct.ShowDataParametars_(" Product_Id=" & Val(DGVRecipe(DGVRecipe.Rows.Count - 1, "Product_Id")) & "")

    '                    If DTPatch.Rows.Count > 0 Then
    '                        DGVRecipe(DGVRecipe.Rows.Count - 1, "IsExpire") = DTPatch.Rows(0)("IsExpire")

    '                        If DGVRecipe(DGVRecipe.Rows.Count - 1, "IsExpire").ToString() = "True" Then

    '                            '*********************************************************************
    '                            DTPatch.Clear()
    '                            DTPatch = ClsPatch.ShowProductIdExpireDate_(Val(DGVRecipe(DGVRecipe.Rows.Count - 1, "Product_Id")), Comp_CostCenter.SelectedValue)
    '                            If DTPatch.Rows.Count = 0 Then
    '                                MessageBox.Show("Sorry This Cost Center Dont Have Expire Patch " & Comp_CostCenter.Text, "Warrning", MessageBoxButtons.OK, MessageBoxIcon.Error)
    '                                DGVRecipe.Rows.Remove(DGVProduction.RowSel)
    '                                Btn_Clear_Click(sender, e)
    '                                Return
    '                            Else
    '                                DGVRecipe(DGVRecipe.Rows.Count - 1, "Patch_Ser") = DTPatch.Rows(0)("Patch_Ser")
    '                                DGVRecipe(DGVRecipe.Rows.Count - 1, "Patch_Name") = DTPatch.Rows(0)("Patch_Name")
    '                            End If
    '                            '**********************************************************************
    '                            'DGVRecipe(DGVRecipe.Rows.Count - 1, "Patch_Ser") = Dt.Rows(0)("")
    '                            'DGVRecipe(DGVRecipe.Rows.Count - 1, "Patch_Name") = Dt.Rows(0)("")
    '                        End If
    '                    Else
    '                        DGVRecipe(DGVRecipe.Rows.Count - 1, "IsExpire") = "False"
    '                    End If



    '                    'DGVRecipe(DGVRecipe.Rows.Count - 1, "Stock_On_Hand") = Dt.Rows(R)("")
    '                    DGVRecipe(DGVRecipe.Rows.Count - 1, "Shortage") = Val(DGVRecipe(DGVRecipe.Rows.Count - 1, "Stock_On_Hand")) - Val(DGVRecipe(DGVRecipe.Rows.Count - 1, "Recipe_Quantity"))
    '                    '  DGVRecipe(DGVRecipe.Rows.Count - 1, "") = Dt.Rows(R)("")

    '                Next

    '                '///////////////////////////////////////////////////////////
    '                Dim Totline As Double = 0
    '                For tt As Integer = 1 To DGVRecipe.Rows.Count - 1
    '                    If Val(DGVRecipe(tt, "Recipe_Product_Id")) = Val(DGVProduction(DGVProduction.RowSel, "Product_Id")) Then
    '                        Totline += Val(DGVRecipe(tt, "Cost_Product"))
    '                    End If

    '                Next
    '                DGVProduction(DGVProduction.RowSel, "Cost_Product") = Math.Round(Totline, 2) 'Math.Round(f.DGVProduct(f.Innt, "Cost_Product"), 2)
    '                '  MessageBox.Show(DGVProduction(DGVProduction.RowSel, "OldCost_Product"))
    '                '   DGVProduction(DGVProduction.RowSel, "OldCost_Product") = Totline
    '                DGVProduction.Rows.Add()
    '            End If
    '        Else
    '            e.Handled = True
    '        End If
    '    End If
    'End Sub
    Public Function DtRecipy() As DataTable
        Dim Dt As New DataTable("RecipyProductions")

        'DGVRecipe.Cols(0).Caption = "Product Code"
        'DGVRecipe.Cols(1).Caption = "Product Name"
        'DGVRecipe.Cols(2).Caption = "Recipe Unit Size"
        'DGVRecipe.Cols(3).Caption = "Unit of Measure"
        'DGVRecipe.Cols(4).Caption = "Required Quantity"
        'DGVRecipe.Cols(5).Caption = "Recipe Stock"
        'DGVRecipe.Cols(6).Caption = "Stock On Hand"
        'DGVRecipe.Cols(7).Caption = "Shortage"
        'DGVRecipe.Cols(8).Caption = "ItemCodRecipy"
        'DGVRecipe.Cols(9).Caption = "u_z"
        'DGVRecipe.Cols(10).Caption = "unt"
        'DGVRecipe.Cols(11).Caption = "Curru_z"
        'DGVRecipe.Cols(12).Caption = "rlt"

        Dt.Columns.Add("Product_Code", GetType(String))
        Dt.Columns.Add("Product_Name", GetType(String))
        Dt.Columns.Add("Current_Unt_Name", GetType(String))
        Dt.Columns.Add("UsedQuantity", GetType(Double))
        Dt.Columns.Add("Required_Quantity", GetType(Double))
        Dt.Columns.Add("Recipe_Quantity", GetType(Double))
        Dt.Columns.Add("Stock_On_Hand", GetType(Double))
        Dt.Columns.Add("Shortage", GetType(Decimal))
        'Dt.Columns.Add("ItemCodRecipy", GetType(String))
        Dt.Columns.Add("Unt_Id", GetType(Integer))
        '  Dt.Columns.Add("Unt_Name", GetType(String))
        Dt.Columns.Add("Unt_GroupId", GetType(Integer))
        Dt.Columns.Add("Unt_Q", GetType(Double))
        Dt.Columns.Add("Current_Unt_Id", GetType(Integer))
        Dt.Columns.Add("Unt_Name", GetType(String))


        Dt.Columns.Add("Current_Unt_Q", GetType(Double))
        Dt.Columns.Add("NetQ_Qsetup_CurrentQ", GetType(Double))

        Dt.Columns.Add("Recipe_Product_Id", GetType(Integer))
        Dt.Columns.Add("Product_Id", GetType(Integer))
        Dt.Columns.Add("IsExpire", GetType(Boolean))
        Dt.Columns.Add("Patch_Ser", GetType(Integer))
        Dt.Columns.Add("Patch_Name", GetType(String))
        Dt.Columns.Add("Cost_Product", GetType(Double))
        Dt.Columns.Add("CostTotalLine", GetType(Double))
        'Recipe_Lost_cost
        Dt.Columns.Add("Recipe_Lost_cost", GetType(Double))
        Return Dt
    End Function
    Public Sub LoadPOSData(IsLoding As Boolean)
        DGV.DataSource = Nothing
        Application.DoEvents()
        If IsLoding Then
            DGV.DataSource = IFC_Cls.LoadPosData(DPFromDate.Value, DpToDate.Value, CInt(Val(Comp_CostCenter.SelectedValue)))
        Else
            DGV.DataSource = IFC_Cls.LoadPosData(DPFromDate.Value, DpToDate.Value, CInt(Val(Comp_CostCenter.SelectedValue)))
        End If
        Application.DoEvents()
        DGV.Rows(0).Height = 30
        '    DGV.Cols("payform").Visible = False
        DGV.Cols("plu").Visible = False
        DGV.Cols("mandant").Caption = "Company ID"
        DGV.Cols("outlet").Caption = "OutLet ID"
        DGV.Cols("center").Caption = "Costcenter ID"
        DGV.Cols("centername").Caption = "Costcenter Name"
        DGV.Cols("plu").Caption = "Product Code"
        DGV.Cols("article").Caption = "Product Name"
        DGV.Cols("Qty").Caption = "Quantity"
        DGV.Cols("price").Caption = "Sales Price"
        DGV.Cols("Amount").Caption = "Total"
        DGV.Cols("discount").Caption = "Discount"
        DGV.Cols("taxamount").Caption = "Tax Amount"
        DGV.Cols("statistdate").Caption = "Date"
        '  DGV.Cols("payformname").Caption = "Method Of payment"
        ' DGV.Cols("billnum").Caption = "Check No"


        DGV.Cols("mandant").Move(0)
        DGV.Cols("outlet").Move(1)
        DGV.Cols("center").Move(2)
        DGV.Cols("centername").Move(3)
        ' DGV.Cols("plu").Move(4)
        DGV.Cols("article").Move(4)
        DGV.Cols("Qty").Move(5)
        DGV.Cols("price").Move(6)
        DGV.Cols("Amount").Move(7)
        DGV.Cols("discount").Move(8)
        DGV.Cols("taxamount").Move(9)
        DGV.Cols("statistdate").Move(10)
        ' DGV.Cols("payformname").Move(11)
        ' DGV.Cols("billnum").Move(12)


        DGV.Cols("mandant").Width = DGV.Width * Val(0.06)
        DGV.Cols("outlet").Width = DGV.Width * Val(0.06)
        DGV.Cols("center").Width = DGV.Width * Val(0.06)
        DGV.Cols("centername").Width = DGV.Width * Val(0.15)
        ' DGV.Cols("plu").Move(4)
        DGV.Cols("article").Width = DGV.Width * Val(0.15)
        DGV.Cols("Qty").Width = DGV.Width * Val(0.06)
        DGV.Cols("price").Width = DGV.Width * Val(0.06)
        DGV.Cols("Amount").Width = DGV.Width * Val(0.1)
        DGV.Cols("discount").Width = DGV.Width * Val(0.1)
        DGV.Cols("taxamount").Width = DGV.Width * Val(0.1)
        DGV.Cols("statistdate").Width = DGV.Width * Val(0.1)
        '  DGV.Cols("payformname").Width = DGV.Width * Val(0.1)
        '  DGV.Cols("billnum").Width = DGV.Width * Val(0.06)
    End Sub
    Public Function GetTable() As DataTable
        Dim DtT As New DataTable
        DtT.Columns.Add("mandant", GetType(Integer))
        DtT.Columns.Add("outlet", GetType(String))
        DtT.Columns.Add("center", GetType(Integer))
        DtT.Columns.Add("centername", GetType(String))
        DtT.Columns.Add("plu", GetType(String))
        DtT.Columns.Add("article", GetType(String))
        DtT.Columns.Add("Qty", GetType(Decimal))
        DtT.Columns.Add("price", GetType(Decimal))
        DtT.Columns.Add("Amount", GetType(Decimal))
        DtT.Columns.Add("discount", GetType(Decimal))
        DtT.Columns.Add("taxamount", GetType(Decimal))
        DtT.Columns.Add("statistdate", GetType(DateTime))
        DtT.Columns.Add("ChDID", GetType(Integer))
        DtT.Columns.Add("IsIFCDone", GetType(Boolean))

        DtT.Rows.Add()
        Return DtT
    End Function
    Public Sub LoadPOSDataManuale(IsLoding As Boolean)
        DGV.DataSource = Nothing
        Application.DoEvents()
        'If IsLoding Then
        '    DGV.DataSource = IFC_Cls.LoadPosData(DPFromDate.Value, DpToDate.Value)
        'Else
        '    DGV.DataSource = IFC_Cls.LoadPosData(DPFromDate.Value, DpToDate.Value)
        'End If

        DGV.DataSource = GetTable
        Application.DoEvents()
        DGV.Rows(0).Height = 30
        '    DGV.Cols("payform").Visible = False
        'DGV.Cols("plu").Visible = False
        DGV.Cols("mandant").Caption = "Company ID"
        DGV.Cols("outlet").Caption = "OutLet ID"
        DGV.Cols("center").Caption = "Costcenter ID"
        DGV.Cols("centername").Caption = "Costcenter Name"
        DGV.Cols("plu").Caption = "Product Code"
        DGV.Cols("article").Caption = "Product Name"
        DGV.Cols("Qty").Caption = "Quantity"
        DGV.Cols("price").Caption = "Sales Price"
        DGV.Cols("Amount").Caption = "Total"
        DGV.Cols("discount").Caption = "Discount"
        DGV.Cols("taxamount").Caption = "Tax Amount"
        DGV.Cols("statistdate").Caption = "Date"
        '  DGV.Cols("payformname").Caption = "Method Of payment"
        ' DGV.Cols("billnum").Caption = "Check No"


        DGV.Cols("mandant").Move(0)
        DGV.Cols("outlet").Move(1)
        DGV.Cols("center").Move(2)
        DGV.Cols("centername").Move(3)
        DGV.Cols("plu").Move(4)
        DGV.Cols("article").Move(5)
        DGV.Cols("Qty").Move(6)
        DGV.Cols("price").Move(7)
        DGV.Cols("Amount").Move(8)
        DGV.Cols("discount").Move(9)
        DGV.Cols("taxamount").Move(10)
        DGV.Cols("statistdate").Move(11)
        ' DGV.Cols("payformname").Move(11)
        ' DGV.Cols("billnum").Move(12)


        DGV.Cols("mandant").Width = DGV.Width * Val(0.06)
        DGV.Cols("outlet").Width = DGV.Width * Val(0.06)
        DGV.Cols("center").Width = DGV.Width * Val(0.06)
        DGV.Cols("centername").Width = DGV.Width * Val(0.1)
        ' DGV.Cols("plu").Move(4)
        DGV.Cols("plu").Width = DGV.Width * Val(0.1)
        DGV.Cols("article").Width = DGV.Width * Val(0.1)
        DGV.Cols("Qty").Width = DGV.Width * Val(0.06)
        DGV.Cols("price").Width = DGV.Width * Val(0.06)
        DGV.Cols("Amount").Width = DGV.Width * Val(0.1)
        DGV.Cols("discount").Width = DGV.Width * Val(0.1)
        DGV.Cols("taxamount").Width = DGV.Width * Val(0.1)
        DGV.Cols("statistdate").Width = DGV.Width * Val(0.1)
        '  DGV.Cols("payformname").Width = DGV.Width * Val(0.1)
        '  DGV.Cols("billnum").Width = DGV.Width * Val(0.06)
    End Sub
    Sub DeleteViewIFC()
        On Error Resume Next
        Dim dt As New DataTable
        dt.Clear()
        Dim Ad As New SqlDataAdapter("SELECT * FROM sys.Views WHERE name ='v_ProcIFC'", Conn.SqlConnPOS)
        Ad.Fill(dt)

        If dt.Rows.Count > 0 Then
            Conn.EXECUT_TxtPOS("Drop view v_ProcIFC")
        End If
    End Sub
    Private Sub IFC_Main_Frm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        DPFromDate.Value = Date.Now
        DpLess.Value = Date.Now
        DpNow.Value = Date.Now
        DpToDate.Value = Date.Now
        'If IsManuel = False Then
        '    DeleteViewIFC()
        '    LoadPOSData(True)
        'Else
        '    LoadPOSDataManuale(True)
        'End If
        ''   Timer1.Enabled = True
        FillCombo()
    End Sub

    Public Sub FillCombo()
        Dim Dt As New DataTable
        Dt.Clear()
        ' Dt = ClsCostCenter.ShowWithFilter_(Val(Frm_Main.TxtStore_Id.Text))

        '    Dt = Cls_Auth.ShowWithFilterDetailsAuth_(Val(Frm_Main.TxtGroupSer.Text), Val(Frm_Main.TxtStore_Id.Text), False, True, True, False)
        Dt = Conn.SELECT_TXT("Select * from POSSetting Order By Company_NamePOS")
        ClsValid.FillCombo(Comp_CostCenter, Dt, Dt.Columns("CostCenter_IdPOS").ToString(), Dt.Columns("CostCenter_NamePOS").ToString())

    End Sub
    Public Sub SetToolsPosition()
        LblFrom.Bounds = GetCellRectangle(0, 0)
        LblTo.Bounds = GetCellRectangle(0, 1)
        '  DpNow.Visible = False

    End Sub
    Private Function GetCellRectangle(ByVal row As Integer, ByVal col As Integer) As Rectangle
        Return Rectangle.Intersect(C1Sizer1.Grid.Rows(row).Bounds, C1Sizer1.Grid.Columns(col).Bounds)
    End Function

    Sub OnlineMoodIFC()
        Try
            Dim Invalid As Boolean
            Dim Dt As New DataTable
            Dt.Clear()
            Dim DpFrom, DpTo As New DateTime
            DpToDate.Value = DPFromDate.Value

            IFC_Cls.DeleteQuantityNow()
            IsProduction = False
            'Dt = IFC_Cls.GetLasttrans_date(DPFromDate.Value)
            'If Dt.Rows.Count > 0 Then
            '    DpLess.Value = Format(Convert.ToDateTime(Dt.Rows(0)("Transaction_Date_Create")), "yyyy/MM/dd HH:mm:ss")

            'Else
            '    DpLess.Value = New Date(Date.Now.Year - 1, Date.Now.Month, Date.Now.Day)
            '    'DpLess.Value = DpLess.Value.Year - 1

            'End If
            'DpNow.Value = New Date(Date.Now.Year, Date.Now.Month, Date.Now.Day)

            'DpFrom = Format(DPFromDate.Value, "yyyy/MM/dd")
            'DpTo = Format(DpToDate.Value, "yyyy/MM/dd")
            '///////////////////////////////////////////// If POS Smart ////////////////////////
            'If DpFrom <= DpLess.Value Then
            '    MessageBox.Show("Sorry Must From Date More Than  " & DpLess.Value.ToString(), "IFC SCM POS", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            '    Return
            'End If

            'If Conn.POS = "Smart POS" Then
            '    If ClosedDay() = False Then
            '        MessageBox.Show("Sorry Smart Pos Not Day Turn  Over At This Date " & DPFromDate.Value.ToShortDateString(), "IFC SCM POS", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            '        Return
            '    End If

            'Else
            '    '///////////////////////////////////////////// If POS Matrix ////////////////////////



            '    If DpTo >= DpNow.Value Then
            '        MessageBox.Show("Sorry Must From Date Less Than  " & DpNow.Value.ToString(), "IFC SCM POS", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            '        Return
            '    End If

            '    If DPFromDate.Value > DpToDate.Value Then
            '        MessageBox.Show("Sorry Must From Date Less Than To Date ", "IFC SCM POS", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            '        Return
            '    End If


            'End If
            LoadPOSDataOnline(False)
            If DGV.Rows.Count > 1 Then
                Dim DtCosLink As New DataTable
                DtCosLink.Clear()
                DtCosLink = ClsSetting.GetCostDataLink()

                For R As Integer = 0 To DtCosLink.Rows.Count - 1
                    Invalid = IFC_Cls.CheckValiDate(Val(DtCosLink.Rows(R)("CostCenter_Id")), DPFromDate.Value, DtCosLink.Rows(R)("CostCenter_Name"))

                    If Invalid = False Then Exit For

                    Invalid = IFC_Cls.CheckValiDate(Val(DtCosLink.Rows(R)("CostCenter_Id")), DpToDate.Value, DtCosLink.Rows(R)("CostCenter_Name"))

                    If Invalid = False Then Exit For
                Next

                If Invalid = False Then Return
                'Dim ValueV As Boolean
                ''/////////////////// Check Period Inventory////////////////////////
                'ValueV = IFC_Cls.CheckValiDate(Val(DtCostcenter.Rows(0)("CostCenter_Id")), Convert.ToDateTime(DGV(Pross, "statistdate")), DtCostcenter.Rows(0)("CostCenter_Name"))
                'If ValueV = False Then
                '    DGV.Rows(Pross).Selected = True
                '    DGvSCM.DataSource = Nothing
                '    F.Close()
                '    MessageBox.Show("Sorry The Perriod Inventory is Closed With This Costcenter " & DtCostcenter.Rows(0)("CostCenter_Name").ToString(), Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Error)
                '    Return False
                'End If
                Conn.EXECUT_Txt("update Patches set usd=0 where CloseOpen=0")
                Invalid = CheckTransActionsSalesOnLine()



                If Invalid = False Then
                    Conn.EXECUT_Txt("update Patches set usd=0 where CloseOpen=0")
                    Frm_Progress.Hide()

                    Return
                Else
                    SaveTransActionsSalesOnline()
                End If
            Else
                Conn.EXECUT_Txt("update Patches set usd=0 where CloseOpen=0")
                ' MessageBox.Show("No Tansactions Found", "IFC SCM POS", MessageBoxButtons.OK, MessageBoxIcon.Stop)
                Return
            End If
            Conn.EXECUT_Txt("update Patches set usd=0 where CloseOpen=0")
            MessageBox.Show("Complete Transaction Sales")

        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub

    Public Sub LoadPOSDataOnline(IsLoding As Boolean)
        DGV.DataSource = Nothing
        Application.DoEvents()
        If IsLoding Then
            DGV.DataSource = IFC_Cls.LoadPosDataOnLine(DPFromDate.Value, DpToDate.Value)
        Else
            DGV.DataSource = IFC_Cls.LoadPosDataOnLine(DPFromDate.Value, DpToDate.Value)
        End If
        Application.DoEvents()
        DGV.Rows(0).Height = 30
        '    DGV.Cols("payform").Visible = False
        DGV.Cols("plu").Visible = False
        DGV.Cols("mandant").Caption = "Company ID"
        DGV.Cols("outlet").Caption = "OutLet ID"
        DGV.Cols("center").Caption = "Costcenter ID"
        DGV.Cols("centername").Caption = "Costcenter Name"
        DGV.Cols("plu").Caption = "Product Code"
        DGV.Cols("article").Caption = "Product Name"
        DGV.Cols("Qty").Caption = "Quantity"
        DGV.Cols("price").Caption = "Sales Price"
        DGV.Cols("Amount").Caption = "Total"
        DGV.Cols("discount").Caption = "Discount"
        DGV.Cols("taxamount").Caption = "Tax Amount"
        DGV.Cols("statistdate").Caption = "Date"
        '  DGV.Cols("payformname").Caption = "Method Of payment"
        ' DGV.Cols("billnum").Caption = "Check No"


        DGV.Cols("mandant").Move(0)
        DGV.Cols("outlet").Move(1)
        DGV.Cols("center").Move(2)
        DGV.Cols("centername").Move(3)
        ' DGV.Cols("plu").Move(4)
        DGV.Cols("article").Move(4)
        DGV.Cols("Qty").Move(5)
        DGV.Cols("price").Move(6)
        DGV.Cols("Amount").Move(7)
        DGV.Cols("discount").Move(8)
        DGV.Cols("taxamount").Move(9)
        DGV.Cols("statistdate").Move(10)
        ' DGV.Cols("payformname").Move(11)
        ' DGV.Cols("billnum").Move(12)


        DGV.Cols("mandant").Width = DGV.Width * Val(0.06)
        DGV.Cols("outlet").Width = DGV.Width * Val(0.06)
        DGV.Cols("center").Width = DGV.Width * Val(0.06)
        DGV.Cols("centername").Width = DGV.Width * Val(0.15)
        ' DGV.Cols("plu").Move(4)
        DGV.Cols("article").Width = DGV.Width * Val(0.15)
        DGV.Cols("Qty").Width = DGV.Width * Val(0.06)
        DGV.Cols("price").Width = DGV.Width * Val(0.06)
        DGV.Cols("Amount").Width = DGV.Width * Val(0.1)
        DGV.Cols("discount").Width = DGV.Width * Val(0.1)
        DGV.Cols("taxamount").Width = DGV.Width * Val(0.1)
        DGV.Cols("statistdate").Width = DGV.Width * Val(0.1)
        '  DGV.Cols("payformname").Width = DGV.Width * Val(0.1)
        '  DGV.Cols("billnum").Width = DGV.Width * Val(0.06)
    End Sub

    Public Sub SaveTransActionsSalesOnline()
        On Error Resume Next
        Dim TransCodeSales, TransCodeProduction, Prosstage As Integer
        Dim PatchTransSales, SqllPatch As String
        Dim PatchTransProduction As String = ""
        Dim CollectionPatchSer As String = String.Empty
        TransCodeSales = IFC_Cls.GetTransCodeSales
        IFC_Cls.Head_Insert(TransCodeSales, Val(DGvSCM.Rows(1)("CostCenter_Id_To")), 0, DGvSCM.Rows(1)("CostCenter_Name_To"), "Sales From POS", TransCodeSales, 0, 0, 0, False, True, 1, 5, 0, 0, True, Convert.ToDateTime(DGvSCM.Rows(1)("Transaction_Date_Create")), Convert.ToDateTime(DGvSCM.Rows(1)("Transaction_Date_Create")), "", 1, "", 9)
        PatchTransSales = IFC_Cls.GetPatchHead(Val(TransCodeSales), 9)
        If IsProduction Then
            TransCodeProduction = IFC_Cls.GetTransCodeProduction
            IFC_Cls.Head_Insert(TransCodeProduction, Val(DGvSCM.Rows(1)("CostCenter_Id_To")), 0, DGvSCM.Rows(1)("CostCenter_Name_To"), "Sales From POS", TransCodeSales, 0, 0, 0, False, True, 1, 5, 0, 0, True, Convert.ToDateTime(DGvSCM.Rows(1)("Transaction_Date_Create")), Convert.ToDateTime(DGvSCM.Rows(1)("Transaction_Date_Create")), "", 1, "", 7)
            PatchTransProduction = IFC_Cls.GetPatchHead(Val(TransCodeProduction), 7)
        End If

        Dim Amount_Bill, Amount_BillProduction, TotalAv, OpenAv, CloseAv, Baseunite As Decimal
        Amount_Bill = 0
        Amount_BillProduction = 0
        Dim DtSOH, DtExpired As New DataTable
        'DtRecipy
        Dim F As New Frm_Progress
        F.Show()
        For R As Integer = 1 To DGvSCM.Rows.Count - 1

            '//////////////// deplay Waiting Form
            Prosstage = ((R) / (DGvSCM.Rows.Count - 1)) * 100
            F.LblStatus.Text = String.Format("Load Transaction To SCM Prossesing..{0}%", Prosstage)
            If R > 100 Or R < 0 Then F.ProgressBar1.Value = R
            F.ProgressBar1.Update()
            F.ProgressBar1.Visible = True
            Thread.Sleep(10)
            ''/////////////////// Create Production And Save It In SOH
            'If Convert.ToBoolean(DGvSCM(R, "Is_Production")) = True Then
            '    DtRecipy.Clear()
            '    DGVRecipe.DataSource = DtRecipy
            '    DGVRecipe.DataSource = IFC_Cls.ShowWithProduct_IdAll(Val(DGvSCM(R, "Product_Id")))
            '    Dim TotAvCost As Decimal = 0
            '    '///////////////// fill Recipy DGV////////////////////////////////////////////
            '    DGVRecipe.DataSource = DtRecipy()
            '    '    FillRecipyProduction()
            '    For L As Integer = 1 To DGVRecipe.Rows.Count - 1
            '        DGVRecipe(L, "Recipe_Quantity") = Math.Abs(DGvSCM(R, "NetQ_Qsetup_CurrentQ"))
            '        DGVRecipe(L, "Recipe_Quantity") = Math.Round(Val(DGVRecipe(L, "UsedQuantity")) * Val(DGVRecipe(L, "Recipe_Quantity")), 2)

            '        DGVRecipe(L, "Recipe_Quantity") = Math.Round(Val(DGVRecipe(L, "Recipe_Quantity")) / Val(DGVRecipe(L, "Unt_Q")), 2)

            '        DGVRecipe(L, "Shortage") = Val(DGVRecipe(L, "Stock_On_Hand")) - Val(DGVRecipe(L, "Recipe_Quantity"))
            '        DGVRecipe(L, "NetQ_Qsetup_CurrentQ") = Val(DGVRecipe(L, "Recipe_Quantity"))
            '        DGVRecipe(L, "CostTotalLine") = Math.Round(Val(DGVRecipe(L, "Recipe_Quantity")) * Val(DGVRecipe(L, "Cost_Product")), 2)
            '        TotAvCost += Convert.ToDouble(DGVRecipe(L, "Cost_Product"))


            '        If DGvSCM(R, "IsExpire").ToString() = "True" And Val(DGvSCM(R, "Patch_Ser")) > 0 Then
            '            'Dim Dt As New DataTable
            '            DtExpired.Clear()
            '            DtExpired = IFC_Cls.ShowProductIdExpireDate_(Val(DGvSCM(R, "Product_Id")), Val(DGvSCM(R, "CostCenter_Id_To")))
            '            If DtExpired.Rows.Count = 0 Then
            '                'MessageBox.Show("Sorry This Cost Center Dont Have Expire Patch " & Comp_CostCenter.Text, "Warrning", MessageBoxButtons.OK, MessageBoxIcon.Error)
            '                'DGVProduction.Rows.Remove(DGVProduction.RowSel)
            '                'Btn_Clear_Click(sender, e)
            '                'Return
            '                DGVRecipe(L, "Patch_Ser") = 0 'DtExpired.Rows(0)("Patch_Ser")
            '                DGVRecipe(L, "Patch_Name") = ""
            '            Else
            '                DGVRecipe(L, "Patch_Ser") = DtExpired.Rows(0)("Patch_Ser")
            '                DGVRecipe(L, "Patch_Name") = DtExpired.Rows(0)("Patch_Name")
            '            End If



            '        End If
            '        '///////////////////// save recipy//////////////////////////////////////
            '        Dim PatchN As Integer
            '        Dim AvCost As Decimal
            '        Dim Dt As New DataTable
            '        AvCost = 0
            '        Dt.Clear()

            '        Dt = IFC_Cls.GetSOHData(Val(DGVRecipe(R, "Product_Id")), Val(DGvSCM(R, "CostCenter_Id_To")))

            '        If Dt.Rows.Count > 0 Then
            '            AvCost = Convert.ToDouble(Dt.Rows(0)("AvCost"))
            '        Else
            '            AvCost = 0
            '        End If

            '        If DGVRecipe(R, "Patch_Name").ToString() <> "" Then
            '            SqllPatch = DGVRecipe(R, "Patch_Name").ToString()
            '            PatchN = Val(DGVRecipe(R, "Patch_Ser"))
            '        Else
            '            SqllPatch = ""
            '            PatchN = 0
            '        End If

            '        Dim Totals As Double = 0
            '        Totals = Convert.ToDouble(DGVRecipe(R, "NetQ_Qsetup_CurrentQ")) * AvCost
            '        Totals = Math.Round(Totals, 2)
            '        '    MessageBox.Show(DGVTransfer(R, "IsExpire"))

            '        'For u As Integer = 0 To DGVTransfer.Cols.Count - 1
            '        '    If IsDBNull(DGVTransfer(R, u)) Then
            '        '        MessageBox.Show(DGVTransfer.Cols(u).Name)
            '        '    End If
            '        'Next


            '        'DGVRecipe.Cols("UsedQuantity").Caption = "Unit of Measure"
            '        'DGVRecipe.Cols("Required_Quantity").Caption = "Required Quantity"

            '        IFC_Cls.Details_Save(Val(TransCodeProduction), Val(DGVRecipe(L, "Product_Id")), DGVRecipe(L, "Product_Code"), DGVRecipe(L, "Product_Name"), 0, Val(DGVRecipe(L, "Recipe_Quantity")), Val(DGVRecipe(L, "Recipe_Quantity")), 0, AvCost, Totals, Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"), Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"), 0, "", 0, "", False, False, PatchN, SqllPatch, DGVRecipe(L, "IsExpire"), Val(DGVRecipe(L, "Unt_Id")), DGVRecipe(L, "Unt_Name"), Val(DGVRecipe(L, "Unt_GroupId")), Val(DGVRecipe(L, "Unt_Q")), Val(DGVRecipe(L, "Current_Unt_Id")), DGVRecipe(L, "Current_Unt_Name"), Val(DGVRecipe(L, "Current_Unt_Q")), Convert.ToDouble(DGVRecipe(L, "NetQ_Qsetup_CurrentQ")), Convert.ToDateTime(DGvSCM(R, "Transaction_Date_Create")), False, True, False, False, PatchTransProduction, False, Convert.ToDateTime(DGvSCM(R, "Transaction_Date_Create")), False, AvCost, Convert.ToDouble(DGVRecipe(L, "Required_Quantity")), Convert.ToDouble(DGVRecipe(L, "UsedQuantity")))

            '    Next

            '    '////////////////////////////////// save Production

            '    IFC_Cls.Details_Save(Val(TransCodeProduction), Val(DGvSCM(R, "Product_Id")), DGvSCM(R, "Product_Code"), DGvSCM(R, "Product_Name"), 0, Val(Math.Abs(DGvSCM(R, "Reciving_Q"))), Val(Math.Abs(DGvSCM(R, "Reciving_Q"))), 0, Convert.ToDouble(Math.Abs(DGvSCM(R, "Cost_Product"))), Convert.ToDouble(Math.Abs(DGvSCM(R, "CostTotalLine"))), Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"), Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"), 0, "", 0, "", False, False, Val(DGvSCM(R, "Patch_Ser")), DGvSCM(R, "Patch_Name"), Convert.ToBoolean(DGvSCM(R, "IsExpire")), Val(DGvSCM(R, "Unt_Id")), DGvSCM(R, "Unt_Name"), Val(DGvSCM(R, "Unt_GroupId")), Val(DGvSCM(R, "Unt_Q")), Val(DGvSCM(R, "Current_Unt_Id")), DGvSCM(R, "Current_Unt_Name"), Val(DGvSCM(R, "Current_Unt_Q")), Convert.ToDouble(DGvSCM(R, "NetQ_Qsetup_CurrentQ")), Convert.ToDateTime(DGvSCM(R, "Transaction_Date_Create")), False, True, False, False, PatchTransProduction, True, Convert.ToDateTime(DGvSCM(R, "Transaction_Date_Create")), True, Convert.ToDouble(DGvSCM(R, "OldCost_Product")))
            'End If
            ''///////////////////////////////////////////////Recipy Production //////////////////////////////////////////////

            If Convert.ToBoolean(DGvSCM(R, "Is_Production")) = True Then
                Grids_Bulid_prod()
                DGVProduction.Rows(1).Selected = True

                GetProductionRecipyData(R)
                Dim SubM As Boolean = RowEffectQuantity(R)
                Submit_Btn_Actions(R, PatchTransProduction, TransCodeProduction, 7, Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"))

            End If

            '////////////////////////////////////////////////////////////////////////////////////////////////////////////////
            '///////////////// Get SOH Open Close Q////////////////////////////////
            TotalAv = 0
            OpenAv = 0
            CloseAv = 0
            Baseunite = 0
            DtSOH.Clear()
            DtSOH = IFC_Cls.GetSOHData(Val(DGvSCM(R, "Product_Id")), Val(DGvSCM(R, "CostCenter_Id_To")))
            TotalAv = DGvSCM(R, "NetQ_Qsetup_CurrentQ") * DtSOH.Rows(0)("AvCost")
            OpenAv = DtSOH.Rows(0)("Quntity")
            CloseAv = DtSOH.Rows(0)("Quntity") + DGvSCM(R, "NetQ_Qsetup_CurrentQ")
            Baseunite = DtSOH.Rows(0)("Item_Unit") * DGvSCM(R, "NetQ_Qsetup_CurrentQ")
            Baseunite = Math.Abs(Baseunite)
            'IFC_Cls.UpdateSOH(Val(DGvSCM(R, "Product_Id")), Val(DGvSCM(R, "CostCenter_Id_To")), CloseAv, Baseunite)

            '//////////////////// Update Patch ///////////////////////////////////////////////////////////////////////

            If Convert.ToBoolean(DGvSCM(R, "IsExpire")) = True And Val(DGvSCM(R, "Patch_Ser")) > 0 Then
                '    IFC_Cls.UpdatePatchExpired(Val(DGvSCM(R, "Patch_Ser")), Convert.ToDecimal(DGvSCM(R, "NetQ_Qsetup_CurrentQ")))
                CollectionPatchSer += DGvSCM(R, "Patch_Ser").ToString()
            End If

            '//////////////////////////////////////////////////////////////////////////////////////////////////////////

            'DGvSCM(R, "CompanyPOS_Id") = Val(DGV(Pross, "mandant"))
            'DGvSCM(R, "CompanyPOS_Name") = DtSetting.Rows(0)("Company_NamePOS")
            'DGvSCM(R, "OutLetPOS_Id") = Val(DGV(Pross, "outlet"))
            'DGvSCM(R, "OutLetPOS_Name") = DtSetting.Rows(0)("Brand_NamePOS")
            'DGvSCM(R, "CostCenterPOS_Id") = Val(DGV(Pross, "center"))
            'DGvSCM(R, "CostCenterPOS_Name") = DGV(Pross, "centername")
            'DGvSCM(R, "CostCenter_Id_To") = Val(DtCostcenter.Rows(0)("CostCenter_Id"))
            'DGvSCM(R, "CostCenter_Name_To") = DtCostcenter.Rows(0)("CostCenter_Name")
            'DGvSCM(R, "Transaction_Code") = 0
            'DGvSCM(R, "Product_Id") = Val(Dtproduct.Rows(0)("Product_Id"))
            'DGvSCM(R, "Product_Code") = DGV(Pross, "plu")
            'DGvSCM(R, "Product_Name") = Dtproduct.Rows(0)("Product_Name")
            'DGvSCM(R, "Reciving_Q") = (Dtproduct.Rows(0)("Unt_Q") / Dtproduct.Rows(0)("Unt_QSales")) * (QCu * -1) 'Convert.ToDouble(DGV(Pross, "Qty")) * Val(-1) 'QCu * -1 '
            'DGvSCM(R, "Cost_Product") = CostAv
            'DGvSCM(R, "CostTotalLine") = DGV(Pross, "price") * QCu ' DGV(Pross, "Amount")
            'DGvSCM(R, "Patch_Ser") = patchSer
            'DGvSCM(R, "Patch_Name") = Patchname
            'DGvSCM(R, "Unt_Id") = Val(Dtproduct.Rows(0)("Unt_Id"))
            'DGvSCM(R, "Unt_Name") = Dtproduct.Rows(0)("Unt_Name")
            'DGvSCM(R, "Unt_GroupId") = Dtproduct.Rows(0)("Unt_GroupId")
            'DGvSCM(R, "Unt_Q") = Dtproduct.Rows(0)("Unt_Q")

            'DGvSCM(R, "Current_Unt_Id") = Val(Dtproduct.Rows(0)("Unt_IdSales"))
            'DGvSCM(R, "Current_Unt_Name") = Dtproduct.Rows(0)("Unt_NameSales")
            'DGvSCM(R, "Current_Unt_Q") = Dtproduct.Rows(0)("Unt_QSales")
            'DGvSCM(R, "NetQ_Qsetup_CurrentQ") = QCu * -1 '* Dtproduct.Rows(0)("Unt_QSales")
            ''DGvSCM(r, "NetQ_Qsetup_CurrentQ") = DGvSCM(r, "NetQ_Qsetup_CurrentQ") / Dtproduct.Rows(0)("Unt_Q") 'QCu '* -1 ' Convert.ToDouble(DGV(Pross, "Qty"))
            ''DGvSCM(r, "NetQ_Qsetup_CurrentQ") = DGvSCM(r, "NetQ_Qsetup_CurrentQ") * -1

            'DGvSCM(R, "Transaction_Id") = 0
            'DGvSCM(R, "Transaction_Date_Create") = Convert.ToDateTime(DGV(Pross, "statistdate"))
            'DGvSCM(R, "Transaction_Submit") = Convert.ToBoolean(True)
            'DGvSCM(R, "TransactionDetails_Ser") = ""
            'DGvSCM(R, "Authulized") = Convert.ToBoolean(True)
            'DGvSCM(R, "SOH") = CloseAv
            'DGvSCM(R, "Open_Q") = OpenAv
            'DGvSCM(R, "Close_Q") = CloseAv
            'DGvSCM(R, "Transaction_Patch") = ""
            'DGvSCM(R, "Check_No") = DGV(Pross, "billnum")
            'DGvSCM(R, "TotalAvg") = QCu * CostAv * Val(-1)
            'DGvSCM(R, "Is_Recipy") = Convert.ToBoolean(Dtproduct.Rows(0)("IsRecipe"))
            'DGvSCM(R, "Is_Production") = Convert.ToBoolean(Dtproduct.Rows(0)("IsProduction"))

            'DGvSCM(R, "MethodOfPayment_Id") = Val(DGV(Pross, "payform"))
            'DGvSCM(R, "MethodOfPayment_Name") = DGV(Pross, "payformname")
            'DGvSCM(R, "Sales_Price") = DGV(Pross, "price")
            'DGvSCM(R, "IsExpire") = Convert.ToBoolean(Dtproduct.Rows(0)("IsExpire"))
            'DGvSCM(R, "ProductionCode") = 0 ' ""
            Dim Serr As Integer = 0
            '/////////////////////////////////////SALES TRANSACTIONS////////////////////////////////////////////////////////////
            'Dim msg As String = ""
            'msg = Val(TransCodeSales) & "," & Val(DGvSCM(R, "Product_Id")) & "," & DGvSCM(R, "Product_Code") & "," & DGvSCM(R, "Product_Name") & "," & Val(0) & "," & Val(DGvSCM(R, "Reciving_Q")) & "," & Val(DGvSCM(R, "Reciving_Q")) & "," & Val(0) & "," & Convert.ToDouble(DGvSCM(R, "Cost_Product")) & "," & Convert.ToDouble(DGvSCM(R, "CostTotalLine")) & "," & Val(DGvSCM(R, "CostCenter_Id_To")) & "," & DGvSCM(R, "CostCenter_Name_To") & "," & Val(DGvSCM(R, "CostCenter_Id_To")) & "," & DGvSCM(R, "CostCenter_Name_To") & "," & 0 & "," & "" & "," & 0 & "," & "" & "," & False & "," & False & "," & Val(DGvSCM(R, "Patch_Ser")) & "," & DGvSCM(R, "Patch_Name") & "," & DGvSCM(R, "IsExpire") & "," & Val(DGvSCM(R, "Unt_Id")) & "," & DGvSCM(R, "Unt_Name") & "," & Val(DGvSCM(R, "Unt_GroupId")) & "," & Val(DGvSCM(R, "Unt_Q")) & "," & Val(DGvSCM(R, "Current_Unt_Id")) & "," & DGvSCM(R, "Current_Unt_Name") & "," & Val(DGvSCM(R, "Current_Unt_Q")) & "," & Convert.ToDouble(DGvSCM(R, "NetQ_Qsetup_CurrentQ")) & "," & DGvSCM(R, "Transaction_Date_Create") & "," & 1 & "," & 1 & "," & False & "," & False & "," & PatchTransSales & "," & True & "," & Convert.ToDateTime(DGvSCM(R, "Transaction_Date_Create"))
            'MessageBox.Show(msg)
            Serr = IFC_Cls.Details_SaveSales(Val(TransCodeSales), Val(DGvSCM(R, "Product_Id")), DGvSCM(R, "Product_Code"), DGvSCM(R, "Product_Name"), Val(0), Val(DGvSCM(R, "Reciving_Q")), Val(DGvSCM(R, "Reciving_Q")), Val(0), Convert.ToDouble(DGvSCM(R, "Cost_Product")), Convert.ToDouble(DGvSCM(R, "CostTotalLine")), Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"), Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"), 0, "", 0, "", False, False, Val(DGvSCM(R, "Patch_Ser")), DGvSCM(R, "Patch_Name"), DGvSCM(R, "IsExpire"), Val(DGvSCM(R, "Unt_Id")), DGvSCM(R, "Unt_Name"), Val(DGvSCM(R, "Unt_GroupId")), Val(DGvSCM(R, "Unt_Q")), Val(DGvSCM(R, "Current_Unt_Id")), DGvSCM(R, "Current_Unt_Name"), Val(DGvSCM(R, "Current_Unt_Q")), Convert.ToDouble(DGvSCM(R, "NetQ_Qsetup_CurrentQ")), DGvSCM(R, "Transaction_Date_Create"), 1, 1, False, False, PatchTransSales, True, Convert.ToDateTime(DGvSCM(R, "Transaction_Date_Create")))



            '//////////////////////////////////////////Save In Sales POS////////
            'msg = ""
            'msg = Val(DGvSCM(R, "CostCenterPOS_Id"))
            'msg = msg & "," & DGvSCM(R, "CostCenterPOS_Name")
            'msg = msg & "," & Val(DGvSCM(R, "CostCenter_Id_To")) & ","
            'msg = msg & DGvSCM(R, "CostCenter_Name_To") & ","
            'msg = msg & DGvSCM(R, "Transaction_Code") & ","
            'msg = msg & Val(DGvSCM(R, "Product_Id")) & ","
            'msg = msg & DGvSCM(R, "Product_Code") & ","
            'msg = msg & DGvSCM(R, "Product_Name") & ","
            'msg = msg & DGvSCM(R, "Reciving_Q") & ","
            'msg = msg & DGvSCM(R, "Cost_Product") & ","
            'msg = msg & DGvSCM(R, "CostTotalLine") & ","
            'msg = msg & Val(DGvSCM(R, "Patch_Ser")) & ","
            'msg = msg & DGvSCM(R, "Patch_Name") & ","
            'msg = msg & Val(DGvSCM(R, "Unt_Id")) & ","
            'msg = msg & DGvSCM(R, "Unt_Name") & ","
            'msg = msg & Val(DGvSCM(R, "Unt_GroupId")) & ","
            'msg = msg & DGvSCM(R, "Unt_Q") & ","
            'msg = msg & Val(DGvSCM(R, "Current_Unt_Id")) & ","
            'msg = msg & DGvSCM(R, "Current_Unt_Name") & ","
            'msg = msg & DGvSCM(R, "Current_Unt_Q") & ","
            'msg = msg & DGvSCM(R, "NetQ_Qsetup_CurrentQ") & ","
            ''   msg = msg & Val(DGvSCM(R, "Transaction_Id")) & ","
            'msg = msg & DGvSCM(R, "Transaction_Date_Create") & ","
            'msg = msg & DGvSCM(R, "Transaction_Submit") & ","
            'msg = msg & Val(Serr) & ","
            'msg = msg & DGvSCM(R, "Authulized") & ","
            'msg = msg & DGvSCM(R, "SOH") & ","
            'msg = msg & DGvSCM(R, "Open_Q") & ","
            'msg = msg & DGvSCM(R, "Close_Q") & ","
            ''  msg = msg & DGvSCM(R, "Transaction_Patch") & ","
            'msg = msg & Val(DGvSCM(R, "Check_No")) & ","
            'msg = msg & DGvSCM(R, "TotalAvg") & ","
            'msg = msg & DGvSCM(R, "Is_Recipy") & ","
            'msg = msg & DGvSCM(R, "Is_Production") & ","
            'msg = msg & Val(DGvSCM(R, "CompanyPOS_Id")) & ","
            'msg = msg & DGvSCM(R, "CompanyPOS_Name") & ","
            'msg = msg & Val(DGvSCM(R, "OutLetPOS_Id")) & ","
            'msg = msg & DGvSCM(R, "OutLetPOS_Name") & ","
            'msg = msg & Val(DGvSCM(R, "MethodOfPayment_Id")) & ","
            'msg = msg & DGvSCM(R, "MethodOfPayment_Name") & ","
            'msg = msg & DGvSCM(R, "Sales_Price") & ","
            'msg = msg & DGvSCM(R, "IsExpire") & ","
            'msg = msg & DGvSCM(R, "ProductionCode")

            'MessageBox.Show(msg)

            'IFC_Cls.SaveSalesPos(Val(DGvSCM(R, "CostCenterPOS_Id")), DGvSCM(R, "CostCenterPOS_Name"), Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"), TransCodeSales, Val(DGvSCM(R, "Product_Id")), DGvSCM(R, "Product_Code"), DGvSCM(R, "Product_Name"), DGvSCM(R, "Reciving_Q"), DGvSCM(R, "Cost_Product"), DGvSCM(R, "CostTotalLine"), Val(DGvSCM(R, "Patch_Ser")), DGvSCM(R, "Patch_Name"), Val(DGvSCM(R, "Unt_Id")), DGvSCM(R, "Unt_Name"), Val(DGvSCM(R, "Unt_GroupId")), DGvSCM(R, "Unt_Q"), Val(DGvSCM(R, "Current_Unt_Id")), DGvSCM(R, "Current_Unt_Name"), DGvSCM(R, "Current_Unt_Q"), DGvSCM(R, "NetQ_Qsetup_CurrentQ"), Val(9), DGvSCM(R, "Transaction_Date_Create"), DGvSCM(R, "Transaction_Submit"), Val(Serr), DGvSCM(R, "Authulized"), DGvSCM(R, "SOH"), DGvSCM(R, "Open_Q"), DGvSCM(R, "Close_Q"), PatchTransSales, Val(DGvSCM(R, "Check_No")), DGvSCM(R, "TotalAvg"), DGvSCM(R, "Is_Recipy"), DGvSCM(R, "Is_Production"), Val(DGvSCM(R, "CompanyPOS_Id")), DGvSCM(R, "CompanyPOS_Name"), Val(DGvSCM(R, "OutLetPOS_Id")), DGvSCM(R, "OutLetPOS_Name"), Val(DGvSCM(R, "MethodOfPayment_Id")), DGvSCM(R, "MethodOfPayment_Name"), DGvSCM(R, "Sales_Price"), DGvSCM(R, "IsExpire"), DGvSCM(R, "ProductionCode"))

            IFC_Cls.SaveSalesPos(Val(DGvSCM(R, "CostCenterPOS_Id")), DGvSCM(R, "CostCenterPOS_Name"), Val(DGvSCM(R, "CostCenter_Id_To")), DGvSCM(R, "CostCenter_Name_To"), TransCodeSales, Val(DGvSCM(R, "Product_Id")), DGvSCM(R, "Product_Code"), DGvSCM(R, "Product_Name"), DGvSCM(R, "Reciving_Q"), DGvSCM(R, "Cost_Product"), DGvSCM(R, "CostTotalLine"), Val(DGvSCM(R, "Patch_Ser")), DGvSCM(R, "Patch_Name"), Val(DGvSCM(R, "Unt_Id")), DGvSCM(R, "Unt_Name"), Val(DGvSCM(R, "Unt_GroupId")), DGvSCM(R, "Unt_Q"), Val(DGvSCM(R, "Current_Unt_Id")), DGvSCM(R, "Current_Unt_Name"), DGvSCM(R, "Current_Unt_Q"), DGvSCM(R, "NetQ_Qsetup_CurrentQ"), Val(9), DGvSCM(R, "Transaction_Date_Create"), DGvSCM(R, "Transaction_Submit"), Val(Serr), DGvSCM(R, "Authulized"), DGvSCM(R, "SOH"), DGvSCM(R, "Open_Q"), DGvSCM(R, "Close_Q"), PatchTransSales, Val(0), DGvSCM(R, "TotalAvg"), DGvSCM(R, "Is_Recipy"), DGvSCM(R, "Is_Production"), Val(DGvSCM(R, "CompanyPOS_Id")), DGvSCM(R, "CompanyPOS_Name"), Val(DGvSCM(R, "OutLetPOS_Id")), DGvSCM(R, "OutLetPOS_Name"), Val(0), "POS Sales", DGvSCM(R, "Sales_Price"), DGvSCM(R, "IsExpire"), DGvSCM(R, "ProductionCode"))
            IFC_Cls.UpdateCHDIDOnline(CInt(Val(DGvSCM(R, "ChDID"))))
        Next
        '////////////////////// Update USE Patch False

        If CollectionPatchSer <> "" Then IFC_Cls.UpdatePatcheUseFalse(CollectionPatchSer)
        '//////////////////////  Update TransHead Amount

        IFC_Cls.UpdateHeadAmount(Val(TransCodeSales), 9, Amount_Bill)

        '/////////////////////Update Production Amount
        If IsProduction Then
            IFC_Cls.UpdateHeadAmount(Val(TransCodeProduction), 7, Amount_BillProduction)
        End If
        F.Close()

    End Sub

    Private Sub Timer1_Tick(sender As Object, e As EventArgs) Handles Timer1.Tick
        OnlineMoodIFC()
    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles BtnExportData.Click
        If Val(Comp_CostCenter.SelectedValue) <= 0 Then
            MessageBox.Show("Please Choose Costcenter", "IFC")
            Return
        End If
        If IsManuel = False Then
            DeleteViewIFC()
            LoadPOSData(True)
        Else
            LoadPOSDataManuale(True)
        End If
        Dim Sql, InQ As String
        InQ = ""
        Dim DtItem, DtCost As New DataTable
        'DtCost.Clear()
        'DtCost = Conn.SELECT_TXT("Select * from  CostCenterLinkPOS Where CostCenter_Id=" & CInt(Val(Comp_CostCenter.SelectedValue)) & "")
        'For c As Integer = 0 To DtCost.Rows.Count - 1
        '    If InQ = "" Then
        '        InQ = DtCost.Rows(c)("")
        '    End If
        'Next
        'If InQ = "" Then
        '    InQ = "0"
        'End If
        Sql = "SELECT       dbo.ProductsTbl.Product_Id, dbo.ProductsTbl.Product_Name, dbo.ProductsTbl.Product_Code, dbo.ItmCostCenterLink.CostCenter_Id, dbo.ProductsTbl.IsSales, "
        Sql = Sql & "     dbo.POSSetting.CostCenter_IdPOS, dbo.POSSetting.Company_IdPOS, dbo.POSSetting.Company_NamePOS, dbo.POSSetting.Brand_IdPOS, dbo.POSSetting.Brand_NamePOS, dbo.POSSetting.CostCenter_NamePOS"
        Sql = Sql & "      From            dbo.CostCenterLinkPOS INNER JOIN"
        Sql = Sql & "      dbo.POSSetting ON dbo.CostCenterLinkPOS.Ser = dbo.POSSetting.Ser INNER JOIN"
        Sql = Sql & "                     dbo.ItmCostCenterLink INNER JOIN"
        Sql = Sql & "     dbo.ProductsTbl ON dbo.ItmCostCenterLink.Product_Id = dbo.ProductsTbl.Product_Id ON dbo.CostCenterLinkPOS.CostCenter_Id = dbo.ItmCostCenterLink.CostCenter_Id"
        Sql = Sql & "    WHERE        (dbo.ProductsTbl.IsSales = 1) AND (dbo.POSSetting.CostCenter_IdPOS =" & CInt(Val(Comp_CostCenter.SelectedValue)) & ")"
        Sql = Sql & "    ORDER BY dbo.ProductsTbl.Product_Code"
        DtItem.Clear()
        DtItem = Conn.SELECT_TXT(Sql)

        For r As Integer = 0 To DtItem.Rows.Count - 1
            If r > 0 Then DGV.Rows.Add()
            DGV(DGV.RowSel, "mandant") = DtItem.Rows(r)("Company_IdPOS") ' "Company ID"
            DGV(DGV.RowSel, "outlet") = DtItem.Rows(r)("Brand_IdPOS") ' "OutLet ID"
            DGV(DGV.RowSel, "center") = DtItem.Rows(r)("CostCenter_IdPOS") ' "Costcenter ID"
            DGV(DGV.RowSel, "centername") = DtItem.Rows(r)("CostCenter_NamePOS") ' "Costcenter Name"
            DGV(DGV.RowSel, "plu") = DtItem.Rows(r)("Product_Code") ' "Product Code"
            DGV(DGV.RowSel, "article") = DtItem.Rows(r)("Product_Name") ' "Product Name"
            DGV(DGV.RowSel, "Qty") = 0 ' "Quantity"
            DGV(DGV.RowSel, "price") = 0 ' "Sales Price"
            DGV(DGV.RowSel, "Amount") = 0 ' "Total"
            DGV(DGV.RowSel, "discount") = 0 ' "Discount"
            DGV(DGV.RowSel, "taxamount") = 0 ' "Tax Amount"
            DGV(DGV.RowSel, "statistdate") = DPFromDate.Value  ' "Date"
        Next
        DGV.Rows(0).Height = 30
        '    DGV.Cols("payform").Visible = False
        'DGV.Cols("plu").Visible = False
        DGV.Cols("mandant").Caption = "Company ID"
        DGV.Cols("outlet").Caption = "OutLet ID"
        DGV.Cols("center").Caption = "Costcenter ID"
        DGV.Cols("centername").Caption = "Costcenter Name"
        DGV.Cols("plu").Caption = "Product Code"
        DGV.Cols("article").Caption = "Product Name"
        DGV.Cols("Qty").Caption = "Quantity"
        DGV.Cols("price").Caption = "Sales Price"
        DGV.Cols("Amount").Caption = "Total"
        DGV.Cols("discount").Caption = "Discount"
        DGV.Cols("taxamount").Caption = "Tax Amount"
        DGV.Cols("statistdate").Caption = "Date"
        '  DGV.Cols("payformname").Caption = "Method Of payment"
        ' DGV.Cols("billnum").Caption = "Check No"


        DGV.Cols("mandant").Move(0)
        DGV.Cols("outlet").Move(1)
        DGV.Cols("center").Move(2)
        DGV.Cols("centername").Move(3)
        DGV.Cols("plu").Move(4)
        DGV.Cols("article").Move(5)
        DGV.Cols("Qty").Move(6)
        DGV.Cols("price").Move(7)
        DGV.Cols("Amount").Move(8)
        DGV.Cols("discount").Move(9)
        DGV.Cols("taxamount").Move(10)
        DGV.Cols("statistdate").Move(11)
        ' DGV.Cols("payformname").Move(11)
        ' DGV.Cols("billnum").Move(12)


        DGV.Cols("mandant").Width = DGV.Width * Val(0.06)
        DGV.Cols("outlet").Width = DGV.Width * Val(0.06)
        DGV.Cols("center").Width = DGV.Width * Val(0.06)
        DGV.Cols("centername").Width = DGV.Width * Val(0.1)
        ' DGV.Cols("plu").Move(4)
        DGV.Cols("plu").Width = DGV.Width * Val(0.1)
        DGV.Cols("article").Width = DGV.Width * Val(0.1)
        DGV.Cols("Qty").Width = DGV.Width * Val(0.06)
        DGV.Cols("price").Width = DGV.Width * Val(0.06)
        DGV.Cols("Amount").Width = DGV.Width * Val(0.1)
        DGV.Cols("discount").Width = DGV.Width * Val(0.1)
        DGV.Cols("taxamount").Width = DGV.Width * Val(0.1)
        DGV.Cols("statistdate").Width = DGV.Width * Val(0.1)
        exportExl()


        DGV.Rows(0).Height = 30
        '    DGV.Cols("payform").Visible = False
        'DGV.Cols("plu").Visible = False
        DGV.Cols("mandant").Caption = "Company ID"
        DGV.Cols("outlet").Caption = "OutLet ID"
        DGV.Cols("center").Caption = "Costcenter ID"
        DGV.Cols("centername").Caption = "Costcenter Name"
        DGV.Cols("plu").Caption = "Product Code"
        DGV.Cols("article").Caption = "Product Name"
        DGV.Cols("Qty").Caption = "Quantity"
        DGV.Cols("price").Caption = "Sales Price"
        DGV.Cols("Amount").Caption = "Total"
        DGV.Cols("discount").Caption = "Discount"
        DGV.Cols("taxamount").Caption = "Tax Amount"
        DGV.Cols("statistdate").Caption = "Date"
        '  DGV.Cols("payformname").Caption = "Method Of payment"
        ' DGV.Cols("billnum").Caption = "Check No"


        DGV.Cols("mandant").Move(0)
        DGV.Cols("outlet").Move(1)
        DGV.Cols("center").Move(2)
        DGV.Cols("centername").Move(3)
        DGV.Cols("plu").Move(4)
        DGV.Cols("article").Move(5)
        DGV.Cols("Qty").Move(6)
        DGV.Cols("price").Move(7)
        DGV.Cols("Amount").Move(8)
        DGV.Cols("discount").Move(9)
        DGV.Cols("taxamount").Move(10)
        DGV.Cols("statistdate").Move(11)
        ' DGV.Cols("payformname").Move(11)
        ' DGV.Cols("billnum").Move(12)


        DGV.Cols("mandant").Width = DGV.Width * Val(0.06)
        DGV.Cols("outlet").Width = DGV.Width * Val(0.06)
        DGV.Cols("center").Width = DGV.Width * Val(0.06)
        DGV.Cols("centername").Width = DGV.Width * Val(0.1)
        ' DGV.Cols("plu").Move(4)
        DGV.Cols("plu").Width = DGV.Width * Val(0.1)
        DGV.Cols("article").Width = DGV.Width * Val(0.1)
        DGV.Cols("Qty").Width = DGV.Width * Val(0.06)
        DGV.Cols("price").Width = DGV.Width * Val(0.06)
        DGV.Cols("Amount").Width = DGV.Width * Val(0.1)
        DGV.Cols("discount").Width = DGV.Width * Val(0.1)
        DGV.Cols("taxamount").Width = DGV.Width * Val(0.1)
        DGV.Cols("statistdate").Width = DGV.Width * Val(0.1)
    End Sub
    Sub exportExl()
        '' // choose file
        Dim dlg As SaveFileDialog = New SaveFileDialog()
        dlg.DefaultExt = "xlsx"
        dlg.FileName = "*.xlsx"
        If (dlg.ShowDialog() <> DialogResult.OK) Then Return
        ''Dim rng1 As C1.Win.C1FlexGrid.CellRange = DGV.GetCellRange(0, 1, 1, 1)
        ''rng1.Data = "Product_Code"
        ''DGV.Styles.Fixed.TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.CenterCenter
        ''// save grid as sheet in the book
        ''  Dim flags As FileFlags = (_chkFixed.Checked)? FileFlags.IncludeFixedCells: FileFlags.None
        'Dim flags As FileFlags = FileFlags.IncludeFixedCells
        ''flags.None
        ''DGV.SaveGrid(dlg.FileName, FileFormatEnum.Excel, FileFlags.IncludeFixedCells)
        'DGV.SaveExcel(dlg.FileName, FileFormatEnum.Excel, FileFlags.IncludeFixedCells)
        '//////////////////////////////////////////////////////////////////////////////////////////////////////////
        DGV.AllowMerging = C1.Win.C1FlexGrid.AllowMergingEnum.Free


        'For c As Integer = 0 To DGV.Cols.Count - 1
        '    DGV.Cols(c).AllowMerging = True
        'Next
        'DGV.Rows.Add()
        'Dim rng1 As C1.Win.C1FlexGrid.CellRange = DGV.GetCellRange(0, 1, 1, 1)
        'rng1.Data = "Product_Code"
        'DGV.Rows(0).StyleNew.BackColor = Color.GreenYellow
        'DGV.Rows(1).StyleNew.BackColor = Color.GreenYellow
        'Dim rng2 As C1.Win.C1FlexGrid.CellRange = DGV.GetCellRange(0, 2, 1, 2)
        'rng2.Data = "Product_Name"
        'Dim rng3 As C1.Win.C1FlexGrid.CellRange = DGV.GetCellRange(0, 3, 1, 3)
        'rng3.Data = "Unt_Name"
        'Dim rng4 As C1.Win.C1FlexGrid.CellRange = DGV.GetCellRange(0, 4, 1, 4)
        'rng4.Data = "SubCategory"

        DGV.Styles.Fixed.TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.CenterCenter


        DGV.SaveExcel(dlg.FileName, C1.Win.C1FlexGrid.FileFlags.IncludeFixedCells Or C1.Win.C1FlexGrid.FileFlags.IncludeMergedRanges Or C1.Win.C1FlexGrid.FileFlags.NoFreezing Or C1.Win.C1FlexGrid.FileFlags.VisibleOnly Or C1.Win.C1FlexGrid.FileFlags.OpenXml)
        System.Diagnostics.Process.Start(dlg.FileName)
        DGV.Rows.Remove(DGV.Rows.Count - 1)
    End Sub
    Function LoadExcelData() As DataTable

        Dim conn As OleDbConnection

        Dim dta As OleDbDataAdapter

        Dim dts As DataSet
        Dim excel As String
        Dim OpenFileDialog As New OpenFileDialog

        OpenFileDialog.InitialDirectory = My.Computer.FileSystem.SpecialDirectories.MyDocuments
        OpenFileDialog.Filter = "All Files (*.*)|*.*|Excel files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv|XLS Files (*.xls)|*xls"

        If (OpenFileDialog.ShowDialog(Me) = System.Windows.Forms.DialogResult.OK) Then

            Dim fi As New FileInfo(OpenFileDialog.FileName)
            Dim FileName As String = OpenFileDialog.FileName
            excel = fi.FullName
            conn = New OleDbConnection("Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + excel + ";Extended Properties=Excel 12.0;")
            dta = New OleDbDataAdapter("Select * From [Sheet1$]", conn)
            dts = New DataSet
            dta.Fill(dts, "[Sheet1$]")
            'DGV.DataSource = dts
            'DGV.DataMember = "[Sheet1$]"
            'conn.Close()
            Return dts.Tables("[Sheet1$]")
        End If
        Return Nothing
    End Function
    Private Sub Btn_ImportData_Click(sender As Object, e As EventArgs) Handles Btn_ImportData.Click
        Dim Dt As New DataTable
        Dt.Clear()

        Dt = LoadExcelData()
        ' Dim AppType As String = Conn.GetApplicationType
        If Dt.Rows.Count > 0 Then
            LoadPOSDataManuale(True)

            For r As Integer = 0 To Dt.Rows.Count - 1
                If r > 0 Then DGV.Rows.Add()

                DGV(DGV.Rows.Count - 1, "mandant") = Dt.Rows(r)("Company ID")
                DGV(DGV.Rows.Count - 1, "outlet") = Dt.Rows(r)("OutLet ID")
                DGV(DGV.Rows.Count - 1, "center") = Dt.Rows(r)("Costcenter ID")
                DGV(DGV.Rows.Count - 1, "centername") = Dt.Rows(r)("Costcenter Name")
                DGV(DGV.Rows.Count - 1, "plu") = Dt.Rows(r)("Product Code")
                DGV(DGV.Rows.Count - 1, "article") = Dt.Rows(r)("Product Name")
                DGV(DGV.Rows.Count - 1, "Qty") = Dt.Rows(r)("Quantity")
                DGV(DGV.Rows.Count - 1, "price") = Dt.Rows(r)("Sales Price")
                DGV(DGV.Rows.Count - 1, "Amount") = Dt.Rows(r)("Total")
                DGV(DGV.Rows.Count - 1, "discount") = Dt.Rows(r)("Discount")
                DGV(DGV.Rows.Count - 1, "taxamount") = Dt.Rows(r)("Tax Amount")
                If IsDBNull(Dt.Rows(r)("Date")) Then
                    DGV(DGV.Rows.Count - 1, "statistdate") = DPFromDate.Value  'Dt.Rows(r)("Date")
                Else
                    DGV(DGV.Rows.Count - 1, "statistdate") = Dt.Rows(r)("Date")
                End If


            Next
            'Dt.Merge(DtUpdate)
            'DtUpdate.Clear()
            'DtUpdate.Merge(Dt)
            'Dim xx As Integer = DtUpdate.Rows.Count - 1
        End If
    End Sub

    Private Sub DPFromDate_ValueChanged(sender As Object, e As EventArgs) Handles DPFromDate.ValueChanged
        DpToDate.Value = DPFromDate.Value
    End Sub
End Class