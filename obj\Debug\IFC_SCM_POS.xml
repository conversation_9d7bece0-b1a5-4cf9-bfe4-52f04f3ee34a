﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
IFC_SCM_POS
</name>
</assembly>
<members>
<member name="T:IFC_SCM_POS.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:IFC_SCM_POS.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:IFC_SCM_POS.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="M:IFC_SCM_POS.IFC_Main_Frm.ProcessAllRecipeRequirements(System.Int32)">
 <summary>
 Processes all recipe requirements from all production items and aggregates quantities
 </summary>
 <param name="ro">Row index for cost center information</param>
</member>
<member name="M:IFC_SCM_POS.IFC_Main_Frm.CollectRecipeIngredientsRecursively(System.Int32,System.Double,System.Collections.Generic.Dictionary{System.Int32,System.Double}@,System.Collections.Generic.Dictionary{System.Int32,System.Data.DataRow}@,System.Int32,System.Collections.Generic.List{System.Int32})">
 <summary>
 Recursively collects recipe ingredients and aggregates quantities
 </summary>
 <param name="currentProductId">The product ID being processed</param>
 <param name="quantity">Quantity of the current product needed</param>
 <param name="aggregatedIngredients">Dictionary to store aggregated quantities</param>
 <param name="ingredientDetails">Dictionary to store ingredient details</param>
 <param name="ro">Row index for cost center information</param>
 <param name="processedProducts">List to track processed products to prevent circular references</param>
</member>
<member name="M:IFC_SCM_POS.IFC_Main_Frm.AddAggregatedIngredientToRecipeGrid(System.Data.DataRow,System.Double,System.Int32)">
 <summary>
 Adds an aggregated ingredient to the recipe grid
 </summary>
 <param name="ingredientRow">DataRow containing ingredient information</param>
 <param name="totalQuantity">Total aggregated quantity needed</param>
 <param name="ro">Row index for cost center information</param>
</member>
<member name="M:IFC_SCM_POS.IFC_Main_Frm.ProcessRecipeRecursively(System.Int32,System.Int32,System.Double,System.Int32,System.Collections.Generic.List{System.Int32})">
 <summary>
 Recursively processes recipe ingredients, expanding nested recipes
 </summary>
 <param name="currentProductId">The product ID being processed</param>
 <param name="rootProductId">The root recipe product ID</param>
 <param name="quantityMultiplier">Multiplier for quantities based on parent recipe requirements</param>
 <param name="ro">Row index for cost center information</param>
 <param name="processedProducts">List to track processed products to prevent circular references</param>
</member>
<member name="M:IFC_SCM_POS.IFC_Main_Frm.AddIngredientToRecipeGrid(System.Data.DataRow,System.Int32,System.Double,System.Int32)">
 <summary>
 Adds a regular ingredient to the recipe grid
 </summary>
 <param name="ingredientRow">DataRow containing ingredient information</param>
 <param name="rootProductId">The root recipe product ID</param>
 <param name="quantityMultiplier">Multiplier for quantities</param>
 <param name="ro">Row index for cost center information</param>
</member>
</members>
</doc>
