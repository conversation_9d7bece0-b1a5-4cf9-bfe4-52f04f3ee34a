﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
IFC_SCM_POS
</name>
</assembly>
<members>
<member name="T:IFC_SCM_POS.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:IFC_SCM_POS.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:IFC_SCM_POS.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="M:IFC_SCM_POS.IFC_Main_Frm.InitializeTempProductionTable">
 <summary>
 Initializes temporary production table to track what's being produced in this transaction
 </summary>
</member>
<member name="M:IFC_SCM_POS.IFC_Main_Frm.GetAvailableQuantityWithProduction(System.Int32,System.Int32)">
 <summary>
 Gets available quantity considering both physical stock and pending production
 </summary>
 <param name="productId">Product ID to check</param>
 <param name="costCenterId">Cost Center ID</param>
 <returns>Available quantity including pending production</returns>
</member>
</members>
</doc>
